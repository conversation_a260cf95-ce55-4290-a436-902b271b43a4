[{"ContainingType": "RnD.BackEnd.API.Controllers.AuthDebugController", "Method": "CheckTableStructure", "RelativePath": "api/auth-debug/check-table-structure", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "RnD.BackEnd.API.Controllers.AuthDebugController", "Method": "GetClaims", "RelativePath": "api/auth-debug/claims", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "RnD.BackEnd.API.Controllers.AuthDebugController", "Method": "CreateDirectUser", "RelativePath": "api/auth-debug/create-direct-user", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "RnD.BackEnd.API.Controllers.AuthDebugController", "Method": "CreateUserTable", "RelativePath": "api/auth-debug/create-table", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "RnD.BackEnd.API.Controllers.AuthDebugController", "Method": "GenerateAzureUser", "RelativePath": "api/auth-debug/generate-azure-user", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "RnD.BackEnd.API.Controllers.AuthDebugController", "Method": "RegisterTestUser", "RelativePath": "api/auth-debug/register-test-user", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "RnD.BackEnd.API.Controllers.AuthDebugController", "Method": "TestConnection", "RelativePath": "api/auth-debug/test-connection", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "RnD.BackEnd.API.Controllers.AuthDebugController", "Method": "GetUsers", "RelativePath": "api/auth-debug/users", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "RnD.BackEnd.API.Controllers.AuthController", "Method": "GetAzureUsers", "RelativePath": "api/auth/azure-users", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.AuthController", "Method": "GetAzureUsersDebug", "RelativePath": "api/auth/azure-users-debug", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "RnD.BackEnd.API.Controllers.AuthController", "Method": "GetAzureUsersDelegated", "RelativePath": "api/auth/azure-users-delegated", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.AuthController", "Method": "GetAzureUsersPublic", "RelativePath": "api/auth/azure-users-public", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "searchText", "Type": "System.String", "IsRequired": false}, {"Name": "email", "Type": "System.String", "IsRequired": false}, {"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.AuthController", "Method": "GetCurrentUser", "RelativePath": "api/auth/me", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "RnD.BackEnd.API.Models.User", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.AuthController", "Method": "GetUserPermissions", "RelativePath": "api/auth/permissions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "RnD.BackEnd.API.Controllers.AuthController", "Method": "RegisterUser", "RelativePath": "api/auth/register", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "RnD.BackEnd.API.Controllers.ClienteController", "Method": "GetCliente", "RelativePath": "api/<PERSON>e", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[RnD.BackEnd.API.Models.Cliente, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.ClienteController", "Method": "PostCliente", "RelativePath": "api/<PERSON>e", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ID", "Type": "System.Int32", "IsRequired": false}, {"Name": "Name", "Type": "System.String", "IsRequired": false}, {"Name": "Contact", "Type": "System.String", "IsRequired": false}, {"Name": "Consumo_Cliente", "Type": "System.Collections.Generic.ICollection`1[[RnD.BackEnd.API.Models.Consumo_Cliente, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": false}], "ReturnTypes": [{"Type": "RnD.BackEnd.API.Models.Cliente", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.ClienteController", "Method": "GetCliente", "RelativePath": "api/Cliente/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "RnD.BackEnd.API.Models.Cliente", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.ClienteController", "Method": "PutCliente", "RelativePath": "api/Cliente/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}, {"Name": "ID", "Type": "System.Int32", "IsRequired": true}, {"Name": "Name", "Type": "System.String", "IsRequired": false}, {"Name": "Contact", "Type": "System.String", "IsRequired": false}, {"Name": "Consumo_Cliente", "Type": "System.Collections.Generic.ICollection`1[[RnD.BackEnd.API.Models.Consumo_Cliente, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "RnD.BackEnd.API.Controllers.ClienteController", "Method": "DeleteCliente", "RelativePath": "api/Cliente/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "RnD.BackEnd.API.Controllers.ClienteSubscricaoController", "Method": "CreateClienteSubscricao", "RelativePath": "api/ClienteSubscricao", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "RnD.BackEnd.API.Controllers.ClienteSubscricaoInputModel", "IsRequired": true}], "ReturnTypes": [{"Type": "RnD.BackEnd.API.Models.ClienteSubscricao", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.ClienteSubscricaoController", "Method": "GetAvailableSubscriptions", "RelativePath": "api/ClienteSubscricao/available", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.ClienteSubscricaoController", "Method": "GetSubscricoesByCliente", "RelativePath": "api/ClienteSubscricao/cliente/{clienteId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "clienteId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.ClienteSubscricaoController", "Method": "GetSubscricoesDetailsByCliente", "RelativePath": "api/ClienteSubscricao/cliente/{clienteId}/details", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "clienteId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.ClienteSubscricaoController", "Method": "DeleteClienteSubscricao", "RelativePath": "api/ClienteSubscricao/cliente/{clienteId}/subscricao/{subscriptionId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "clienteId", "Type": "System.Int32", "IsRequired": true}, {"Name": "subscriptionId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "RnD.BackEnd.API.Controllers.ConsumoController", "Method": "GetConsumo", "RelativePath": "api/Consumo", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[RnD.BackEnd.API.Models.Consumo, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.ConsumoController", "Method": "PostConsumo", "RelativePath": "api/Consumo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ID", "Type": "System.Int32", "IsRequired": false}, {"Name": "ClienteID", "Type": "System.Int32", "IsRequired": false}, {"Name": "DataInicio", "Type": "System.DateTime", "IsRequired": false}, {"Name": "DataFim", "Type": "System.DateTime", "IsRequired": false}, {"Name": "CountryListTotal", "Type": "System.Decimal", "IsRequired": false}, {"Name": "CountryResellerTotal", "Type": "System.Decimal", "IsRequired": false}, {"Name": "CountryCustomerTotal", "Type": "System.Decimal", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "Regiao", "Type": "System.String", "IsRequired": false}, {"Name": "<PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "Consumo_Cliente", "Type": "System.Collections.Generic.ICollection`1[[RnD.BackEnd.API.Models.Consumo_Cliente, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": false}], "ReturnTypes": [{"Type": "RnD.BackEnd.API.Models.Consumo", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.Consumo_ClienteController", "Method": "GetConsumo_Cliente", "RelativePath": "api/Consumo_Cliente", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[RnD.BackEnd.API.Models.Consumo_Cliente, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.Consumo_ClienteController", "Method": "PostConsumo_Cliente", "RelativePath": "api/Consumo_Cliente", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ID", "Type": "System.Int32", "IsRequired": false}, {"Name": "ClienteID", "Type": "System.Int32", "IsRequired": false}, {"Name": "Cliente.ID", "Type": "System.Int32", "IsRequired": false}, {"Name": "Cliente.Name", "Type": "System.String", "IsRequired": false}, {"Name": "Cliente.Contact", "Type": "System.String", "IsRequired": false}, {"Name": "Cliente.Consumo_Cliente", "Type": "System.Collections.Generic.ICollection`1[[RnD.BackEnd.API.Models.Consumo_Cliente, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": false}, {"Name": "ConsumoID", "Type": "System.Int32", "IsRequired": false}, {"Name": "Consumo.ID", "Type": "System.Int32", "IsRequired": false}, {"Name": "Consumo.ClienteID", "Type": "System.Int32", "IsRequired": false}, {"Name": "Consumo.DataInicio", "Type": "System.DateTime", "IsRequired": false}, {"Name": "Consumo.DataFim", "Type": "System.DateTime", "IsRequired": false}, {"Name": "Consumo.CountryListTotal", "Type": "System.Decimal", "IsRequired": false}, {"Name": "Consumo.CountryResellerTotal", "Type": "System.Decimal", "IsRequired": false}, {"Name": "Consumo.CountryCustomerTotal", "Type": "System.Decimal", "IsRequired": false}, {"Name": "Consumo.TipoServico", "Type": "System.String", "IsRequired": false}, {"Name": "Consumo.Regiao", "Type": "System.String", "IsRequired": false}, {"Name": "Consumo.Moeda", "Type": "System.String", "IsRequired": false}, {"Name": "Consumo.Consumo_Cliente", "Type": "System.Collections.Generic.ICollection`1[[RnD.BackEnd.API.Models.Consumo_Cliente, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": false}], "ReturnTypes": [{"Type": "RnD.BackEnd.API.Models.Consumo_Cliente", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.Consumo_ClienteController", "Method": "GetConsumosByClienteId", "RelativePath": "api/Consumo_Cliente/cliente/{clienteId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "clienteId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[RnD.BackEnd.API.Models.Consumo_Cliente, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.ConsumoController", "Method": "GetConsumo", "RelativePath": "api/Consumo/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "RnD.BackEnd.API.Models.Consumo", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.ConsumoController", "Method": "GetConsumoFromExcel", "RelativePath": "api/Consumo/FromExcel", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[RnD.BackEnd.API.Models.Consumo, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.ConsumoController", "Method": "UploadExcel", "RelativePath": "api/Consumo/upload", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Object", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.ConsumoExcelAzureController", "Method": "UploadExcel", "RelativePath": "api/ConsumoExcelAzure/upload", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Object", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.ConsumoPDFController", "Method": "GetAll", "RelativePath": "api/ConsumoPDF", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[RnD.BackEnd.API.Models.ConsumoPDF, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.ConsumoPDFController", "Method": "GetByClienteId", "RelativePath": "api/ConsumoPDF/cliente/{clienteId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "clienteId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[RnD.BackEnd.API.Models.ConsumoPDF, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.ConsumoPDFController", "Method": "GetCrayonPDFsExtractedData", "RelativePath": "api/ConsumoPDF/crayon-extracted-data", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Object", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.ConsumoPDFController", "Method": "GetCrayonPDFsRawText", "RelativePath": "api/ConsumoPDF/crayon-raw-text", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Object", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.ConsumoPDFController", "Method": "DebugPeriods", "RelativePath": "api/ConsumoPDF/debug-periods/{fileName}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "fileName", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Object", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.ConsumoPDFController", "Method": "ProcessarPDF", "RelativePath": "api/ConsumoPDF/processar/{fileName}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "fileName", "Type": "System.String", "IsRequired": true}, {"Name": "forceReprocessing", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Object", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.ConsumoPDFController", "Method": "TestAllPDFs", "RelativePath": "api/ConsumoPDF/test-all-pdfs", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Object", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.ConsumoPDFController", "Method": "TestPDF2023", "RelativePath": "api/ConsumoPDF/test-pdf-2023/{fileName}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "fileName", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Object", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.ConsumoPDFController", "Method": "TestPDF", "RelativePath": "api/ConsumoPDF/test-pdf/{fileName}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "fileName", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Object", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.ConsumoPDFController", "Method": "UploadPDF", "RelativePath": "api/ConsumoPDF/upload", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Object", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.ConsumoSubscricaoController", "Method": "GetConsumos", "RelativePath": "api/ConsumoSubscricao", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[RnD.BackEnd.API.Models.ConsumoSubscricao, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.ConsumoSubscricaoController", "Method": "GetConsumosByCliente", "RelativePath": "api/ConsumoSubscricao/cliente/{clienteId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "clienteId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[RnD.BackEnd.API.Models.ConsumoSubscricao, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.ConsumoSubscricaoController", "Method": "GetConsumosSummaryByCliente", "RelativePath": "api/ConsumoSubscricao/cliente/{clienteId}/summary", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "clienteId", "Type": "System.Int32", "IsRequired": true}, {"Name": "startDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "RnD.BackEnd.API.Controllers.ConsumoSubscricaoController", "Method": "GetConsumosByPeriod", "RelativePath": "api/ConsumoSubscricao/period", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "startDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "endDate", "Type": "System.DateTime", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[RnD.BackEnd.API.Models.ConsumoSubscricao, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.ConsumoSubscricaoController", "Method": "GetConsumosBySubscription", "RelativePath": "api/ConsumoSubscricao/subscription/{subscriptionId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "subscriptionId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[RnD.BackEnd.API.Models.ConsumoSubscricao, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.ConsumoSubscricaoController", "Method": "GetConsumosSummary", "RelativePath": "api/ConsumoSubscricao/summary", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "RnD.BackEnd.API.Controllers.ConsumoSubscricaoExcelController", "Method": "ProcessarExcel", "RelativePath": "api/ConsumoSubscricaoExcel/processar/{fileName}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "fileName", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[RnD.BackEnd.API.Models.ConsumoSubscricaoExcelModel, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.DataEntryController", "Method": "GetDataEntryEntities", "RelativePath": "api/DataEntry", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[RnD.BackEnd.API.Model.DataEntry.EntityListDto, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "RnD.BackEnd.Logging.ServiceOutput", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.DynamicEntityController", "Method": "Add", "RelativePath": "api/DynamicEntity", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "RnD.BackEnd.Logging.ServiceOutput", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.DynamicEntityController", "Method": "Update", "RelativePath": "api/DynamicEntity", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}, {"Name": "etag", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 412}, {"Type": "RnD.BackEnd.Logging.ServiceOutput", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.DynamicEntityController", "Method": "List", "RelativePath": "api/DynamicEntity", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "pageNumber", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "maxItems", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "sortField", "Type": "System.String", "IsRequired": false}, {"Name": "sortAscending", "Type": "System.Boolean", "IsRequired": false}, {"Name": "continuationToken", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Collections.Generic.List`1[[System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "RnD.BackEnd.Logging.ServiceOutput", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.DynamicEntityController", "Method": "Delete", "RelativePath": "api/DynamicEntity/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "RnD.BackEnd.Logging.ServiceOutput", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.DynamicEntityController", "Method": "Get", "RelativePath": "api/DynamicEntity/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "RnD.BackEnd.Logging.ServiceOutput", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.EmailController", "Method": "SendCodeAcademyEmail", "RelativePath": "api/Email/SendCodeAcademyEmail", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "RnD.BackEnd.API.Model.Email.EmailMessageDto`1[[RnD.BackEnd.API.Model.Email.CodeAcademyTemplateDto, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": [{"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "RnD.BackEnd.Logging.ServiceOutput", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.EmailController", "Method": "SendDefaultTemplateEmail", "RelativePath": "api/Email/SendDefaultTemplateEmail", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "RnD.BackEnd.API.Model.Email.EmailMessageDto`1[[RnD.BackEnd.API.Model.Email.EmailDto, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": [{"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "RnD.BackEnd.Logging.ServiceOutput", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.EmailController", "Method": "SendDefaultTemplateEmailWithAttachments", "RelativePath": "api/Email/SendDefaultTemplateEmailWithAttachments", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "Attachments", "Type": "System.Collections.Generic.List`1[[Microsoft.AspNetCore.Http.IFormFile, Microsoft.AspNetCore.Http.Features, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60]]", "IsRequired": false}, {"Name": "TemplateData.Message", "Type": "System.String", "IsRequired": false}, {"Name": "TemplateData.Subject", "Type": "System.String", "IsRequired": false}, {"Name": "Recipients", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "RecipientsCC", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "RecipientsBCC", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "RnD.BackEnd.Logging.ServiceOutput", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.EmailController", "Method": "SendTemplatedEmail", "RelativePath": "api/Email/SendTemplatedEmail", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "RnD.BackEnd.API.Model.Email.SpecificTemplateDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "RnD.BackEnd.Logging.ServiceOutput", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.LogProcessamentoController", "Method": "GetLogs", "RelativePath": "api/LogProcessamento", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[RnD.BackEnd.API.Models.LogProcessamento, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.LogProcessamentoController", "Method": "GetLogsPorArquivo", "RelativePath": "api/LogProcessamento/arquivo/{fileId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "fileId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[RnD.BackEnd.API.Models.LogProcessamento, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.LogProcessamentoController", "Method": "GetArquivosProcessados", "RelativePath": "api/LogProcessamento/arquivos", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[RnD.BackEnd.API.Models.ArquivoProcessado, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.LogProcessamentoController", "Method": "GetArquivoProcessado", "RelativePath": "api/LogProcessamento/arquivos/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "RnD.BackEnd.API.Models.ArquivoProcessado", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.LogProcessamentoController", "Method": "GetLogsPorTipo", "RelativePath": "api/LogProcessamento/tipo/{tipo}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "tipo", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[RnD.BackEnd.API.Models.LogProcessamento, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.PermissionsController", "Method": "GetPermissions", "RelativePath": "api/permissions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[RnD.BackEnd.API.Models.Permission, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.PermissionsController", "Method": "CreatePermission", "RelativePath": "api/permissions", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.Int32", "IsRequired": false}, {"Name": "Name", "Type": "System.String", "IsRequired": false}, {"Name": "UserPermissions", "Type": "System.Collections.Generic.ICollection`1[[RnD.BackEnd.API.Models.UserPermission, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": false}], "ReturnTypes": [{"Type": "RnD.BackEnd.API.Models.Permission", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.SubscriptionController", "Method": "GetAllSubscriptions", "RelativePath": "api/Subscription", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[RnD.BackEnd.API.Models.Subscription, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.SubscriptionController", "Method": "GetAvailableSubscriptions", "RelativePath": "api/Subscription/available", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[RnD.BackEnd.API.Models.Subscription, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.UserPermissionsController", "Method": "AssignPermissionToUser", "RelativePath": "api/user-permissions", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": false}, {"Name": "permissionId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "RnD.BackEnd.API.Controllers.UserPermissionsController", "Method": "RemovePermissionFromUser", "RelativePath": "api/user-permissions", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": false}, {"Name": "permissionId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "RnD.BackEnd.API.Controllers.UserPermissionsController", "Method": "GetUserPermissions", "RelativePath": "api/user-permissions/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "RnD.BackEnd.API.Controllers.UserController", "Method": "AddAzureUsers", "RelativePath": "api/user/add-azure-users", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "RnD.BackEnd.API.Controllers.AddAzureUsersRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "RnD.BackEnd.API.Controllers.UserController", "Method": "AddAzureUsersPublic", "RelativePath": "api/user/add-azure-users-public", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "RnD.BackEnd.API.Controllers.AddAzureUsersRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "RnD.BackEnd.API.Controllers.UserController", "Method": "AddAzureUsersTest", "RelativePath": "api/user/add-azure-users-test", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "RnD.BackEnd.API.Controllers.AddAzureUsersRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "RnD.BackEnd.API.Controllers.UserController", "Method": "GetAuthInfo", "RelativePath": "api/user/auth-info", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "RnD.BackEnd.API.Controllers.UserController", "Method": "FallbackRegister", "RelativePath": "api/user/fallback-register", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "RnD.BackEnd.API.Controllers.UserController", "Method": "ForceRegisterUser", "RelativePath": "api/user/force-register", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "RnD.BackEnd.API.Controllers.UserRegisterRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "RnD.BackEnd.API.Controllers.UserController", "Method": "GetUserDetails", "RelativePath": "api/user/GetUserDetails", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "username", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "RnD.BackEnd.API.Controllers.UserController", "Method": "GetCurrentUser", "RelativePath": "api/user/me", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "RnD.BackEnd.API.Controllers.UserController", "Method": "RegisterUser", "RelativePath": "api/user/register", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "RnD.BackEnd.API.Controllers.UsersController", "Method": "GetUsers", "RelativePath": "api/users", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[RnD.BackEnd.API.Models.User, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.UsersController", "Method": "CreateUser", "RelativePath": "api/users", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "UserID", "Type": "System.Int32", "IsRequired": false}, {"Name": "Username", "Type": "System.String", "IsRequired": false}, {"Name": "Email", "Type": "System.String", "IsRequired": false}, {"Name": "AzureId", "Type": "System.String", "IsRequired": false}, {"Name": "UserPermissions", "Type": "System.Collections.Generic.ICollection`1[[RnD.BackEnd.API.Models.UserPermission, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": false}], "ReturnTypes": [{"Type": "RnD.BackEnd.API.Models.User", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.UsersController", "Method": "GetUser", "RelativePath": "api/users/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "RnD.BackEnd.API.Models.User", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.UsersController", "Method": "UpdateUser", "RelativePath": "api/users/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "UserID", "Type": "System.Int32", "IsRequired": false}, {"Name": "Username", "Type": "System.String", "IsRequired": false}, {"Name": "Email", "Type": "System.String", "IsRequired": false}, {"Name": "AzureId", "Type": "System.String", "IsRequired": false}, {"Name": "UserPermissions", "Type": "System.Collections.Generic.ICollection`1[[RnD.BackEnd.API.Models.UserPermission, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "RnD.BackEnd.API.Controllers.UsersController", "Method": "DeleteUser", "RelativePath": "api/users/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "RnD.BackEnd.API.Controllers.UsersController", "Method": "UpdateUserAlternative", "RelativePath": "api/users/update/{id}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "Username", "Type": "System.String", "IsRequired": false}, {"Name": "Email", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "RnD.BackEnd.API.Models.User", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.VehicleController", "Method": "AddAsync", "RelativePath": "api/Vehicle", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "RnD.BackEnd.API.Model.Vehicle.CreateVehicleDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[RnD.BackEnd.API.Model.Vehicle.VehicleDto, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[RnD.BackEnd.API.Model.Vehicle.VehicleDto, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "RnD.BackEnd.Logging.ServiceOutput", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.VehicleController", "Method": "ListAsync", "RelativePath": "api/Vehicle", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "brandId", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "modelName", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "fuelType", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "version", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "year", "Type": "System.Collections.Generic.List`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "pageNumber", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "maxItems", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "sortField", "Type": "System.String", "IsRequired": false}, {"Name": "sortAscending", "Type": "System.Boolean", "IsRequired": false}, {"Name": "continuationToken", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Collections.Generic.List`1[[RnD.BackEnd.API.Model.Vehicle.VehicleDto, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "RnD.BackEnd.Logging.ServiceOutput", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.VehicleController", "Method": "UpdateAsync", "RelativePath": "api/Vehicle/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "RnD.BackEnd.API.Model.Vehicle.VehicleDto", "IsRequired": true}, {"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "etag", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[RnD.BackEnd.API.Model.Vehicle.VehicleDto, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 409}, {"Type": "RnD.BackEnd.Logging.ServiceOutput", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.VehicleController", "Method": "Patch<PERSON><PERSON>", "RelativePath": "api/Vehicle/{id}", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "request", "Type": "Microsoft.AspNetCore.JsonPatch.Operations.Operation[]", "IsRequired": true}], "ReturnTypes": [{"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[RnD.BackEnd.API.Model.Vehicle.VehicleDto, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "RnD.BackEnd.Logging.ServiceOutput", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.VehicleController", "Method": "DeleteAsync", "RelativePath": "api/Vehicle/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 204}, {"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "RnD.BackEnd.Logging.ServiceOutput", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.VehicleController", "Method": "GetAsync", "RelativePath": "api/Vehicle/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[RnD.BackEnd.API.Model.Vehicle.VehicleDto, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "RnD.BackEnd.Logging.ServiceOutput", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.VehicleController", "Method": "AddOrUpdateAsync", "RelativePath": "api/Vehicle/addorupdate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "RnD.BackEnd.API.Model.Vehicle.VehicleDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[RnD.BackEnd.API.Model.Vehicle.VehicleDto, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "RnD.BackEnd.Logging.ServiceOutput", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.VehicleController", "Method": "AddManyAsync", "RelativePath": "api/Vehicle/many", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "System.Collections.Generic.List`1[[RnD.BackEnd.API.Model.Vehicle.CreateVehicleDto, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": [{"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Collections.Generic.List`1[[RnD.BackEnd.API.Model.Vehicle.VehicleDto, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "RnD.BackEnd.Logging.ServiceOutput", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.WeatherForecastController", "Method": "AddAsync", "RelativePath": "api/WeatherForecast", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "RnD.BackEnd.API.Model.Weather.CreateWeatherDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[RnD.BackEnd.API.Model.Weather.WeatherDto, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "RnD.BackEnd.Logging.ServiceOutput", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.WeatherForecastController", "Method": "ListAsync", "RelativePath": "api/WeatherForecast", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": false}, {"Name": "location", "Type": "System.String", "IsRequired": false}, {"Name": "itemsPerPage", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "page", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "sortField", "Type": "System.String", "IsRequired": false}, {"Name": "sortAscending", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IList`1[[RnD.BackEnd.API.Model.Weather.WeatherDto, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "RnD.BackEnd.Logging.ServiceOutput", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.WeatherForecastController", "Method": "UpdateAsync", "RelativePath": "api/WeatherForecast/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "RnD.BackEnd.API.Model.Weather.WeatherDto", "IsRequired": true}, {"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "rowversion", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[RnD.BackEnd.API.Model.Weather.WeatherDto, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 409}, {"Type": "RnD.BackEnd.Logging.ServiceOutput", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.WeatherForecastController", "Method": "Patch<PERSON><PERSON>", "RelativePath": "api/WeatherForecast/{id}", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "request", "Type": "Microsoft.AspNetCore.JsonPatch.Operations.Operation[]", "IsRequired": true}], "ReturnTypes": [{"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[RnD.BackEnd.API.Model.Weather.WeatherDto, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "RnD.BackEnd.Logging.ServiceOutput", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.WeatherForecastController", "Method": "DeleteAsync", "RelativePath": "api/WeatherForecast/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "RnD.BackEnd.Logging.ServiceOutput", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.WeatherForecastController", "Method": "GetAsync", "RelativePath": "api/WeatherForecast/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[RnD.BackEnd.API.Model.Weather.WeatherDto, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "RnD.BackEnd.Logging.ServiceOutput", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.WeatherForecastController", "Method": "AddWithTransactionSample", "RelativePath": "api/WeatherForecast/transaction", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Collections.Generic.List`1[[RnD.BackEnd.API.Model.Weather.WeatherDto, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "RnD.BackEnd.Logging.ServiceOutput", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.WorkflowController", "Method": "GetDefinitions", "RelativePath": "api/Workflow/definitions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "SourceType", "Type": "System.String", "IsRequired": false}, {"Name": "WorkflowType", "Type": "System.String", "IsRequired": false}, {"Name": "FetchInactive", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Collections.Generic.IList`1[[RnD.BackEnd.API.Model.Workflow.WorkflowDefinitionDto, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "RnD.BackEnd.Logging.ServiceOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.WorkflowController", "Method": "GetInstance", "RelativePath": "api/Workflow/instance/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[RnD.BackEnd.API.Model.Workflow.WorkflowInstanceDto, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "RnD.BackEnd.Logging.ServiceOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.WorkflowController", "Method": "CancelWorkflow", "RelativePath": "api/Workflow/instance/{instanceId}/{userId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "instanceId", "Type": "System.Guid", "IsRequired": true}, {"Name": "userId", "Type": "System.String", "IsRequired": true}, {"Name": "request", "Type": "RnD.BackEnd.API.Model.Workflow.WorkflowEmailDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "RnD.BackEnd.Logging.ServiceOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.WorkflowController", "Method": "GetUserInstances", "RelativePath": "api/Workflow/instances", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "SourceType", "Type": "System.String", "IsRequired": false}, {"Name": "WorkflowStatus", "Type": "System.String", "IsRequired": false}, {"Name": "WorkflowType", "Type": "System.String", "IsRequired": false}, {"Name": "WorkflowName", "Type": "System.String", "IsRequired": false}, {"Name": "Purpose", "Type": "System.String", "IsRequired": false}, {"Name": "SortField", "Type": "System.String", "IsRequired": false}, {"Name": "SortDirection", "Type": "System.String", "IsRequired": false}, {"Name": "ItemsPerPage", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CurrentPage", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Collections.Generic.IList`1[[RnD.BackEnd.API.Model.Workflow.WorkflowInstancesListDto, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "RnD.BackEnd.Logging.ServiceOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.WorkflowController", "Method": "DeleteOrCancelWorkflowsBySourceId", "RelativePath": "api/Workflow/instances/deleteOrCancel/{sourceId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "sourceId", "Type": "System.String", "IsRequired": true}, {"Name": "request", "Type": "RnD.BackEnd.API.Model.Workflow.WorkflowEmailDto", "IsRequired": true}, {"Name": "isDeleteOperation", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "RnD.BackEnd.Logging.ServiceOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.WorkflowController", "Method": "GetInstances", "RelativePath": "api/Workflow/instances/list", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "RnD.BackEnd.API.Model.Workflow.WorkflowInstanceBySourceIdsDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Collections.Generic.IList`1[[RnD.BackEnd.API.Model.Workflow.WorkflowInstanceDto, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "RnD.BackEnd.Logging.ServiceOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.WorkflowController", "Method": "ReassingTask", "RelativePath": "api/Workflow/task/reassign", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "RnD.BackEnd.API.Model.Workflow.ReassignTaskDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "RnD.BackEnd.Logging.ServiceOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.WorkflowController", "Method": "UpdateTask", "RelativePath": "api/Workflow/task/update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "RnD.BackEnd.API.Model.Workflow.UpdateTaskDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "RnD.BackEnd.Logging.ServiceOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.WorkflowController", "Method": "GetTasks", "RelativePath": "api/Workflow/tasks", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "SourceType", "Type": "System.String", "IsRequired": false}, {"Name": "UserId", "Type": "System.String", "IsRequired": false}, {"Name": "TaskStatus", "Type": "System.String", "IsRequired": false}, {"Name": "TaskCompleted", "Type": "System.Boolean", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "WorkflowName", "Type": "System.String", "IsRequired": false}, {"Name": "WorkflowStatus", "Type": "System.String", "IsRequired": false}, {"Name": "SortField", "Type": "System.String", "IsRequired": false}, {"Name": "SortDirection", "Type": "System.String", "IsRequired": false}, {"Name": "ItemsPerPage", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CurrentPage", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[RnD.BackEnd.API.Model.Workflow.WorkflowTasksListDto, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "RnD.BackEnd.Logging.ServiceOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.WorkflowController", "Method": "GetTask", "RelativePath": "api/Workflow/tasks/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[RnD.BackEnd.API.Model.Workflow.WorkflowTaskDto, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "RnD.BackEnd.Logging.ServiceOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.WorkflowController", "Method": "UpdateTasks", "RelativePath": "api/Workflow/tasks/update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "System.Collections.Generic.List`1[[RnD.BackEnd.API.Model.Workflow.UpdateTaskDto, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": [{"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Collections.Generic.List`1[[RnD.BackEnd.API.Model.Workflow.UpdateTaskOutputDto, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "RnD.BackEnd.Logging.ServiceOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.WorkflowController", "Method": "TriggerAutomaticWorkflow", "RelativePath": "api/Workflow/trigger/automatic", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "RnD.BackEnd.API.Model.Workflow.StartAutomaticWorkflowDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[RnD.BackEnd.API.Model.Workflow.WorkflowInstanceDto, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "RnD.BackEnd.Logging.ServiceOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "RnD.BackEnd.API.Controllers.WorkflowController", "Method": "StartWorkflow", "RelativePath": "api/Workflow/trigger/start", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "System.Collections.Generic.List`1[[RnD.BackEnd.API.Model.Workflow.StartWorkflowDto, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": [{"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[RnD.BackEnd.API.Model.Workflow.WorkflowInstanceDto, RnD.BackEnd.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "RnD.BackEnd.API.Model.Generic.ApiOutput`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "RnD.BackEnd.Logging.ServiceOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}]