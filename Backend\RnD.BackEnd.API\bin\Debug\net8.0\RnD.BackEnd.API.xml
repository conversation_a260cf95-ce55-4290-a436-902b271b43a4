<?xml version="1.0"?>
<doc>
    <assembly>
        <name>RnD.BackEnd.API</name>
    </assembly>
    <members>
        <member name="M:RnD.BackEnd.API.Controllers.ConsumoController.UploadExcel(Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            Faz upload de um arquivo Excel para processamento no container Reseller
            </summary>
            <param name="file">Arquivo Excel a ser processado</param>
            <returns>Nome do arquivo no Azure Storage</returns>
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.ConsumoExcelAzureController.UploadExcel(Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            Faz upload de um arquivo Excel para processamento no container Azure
            </summary>
            <param name="file">Arquivo Excel a ser processado</param>
            <returns>Nome do arquivo no Azure Storage</returns>
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.ConsumoPDFController.UploadPDF(Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            Faz upload de um arquivo PDF para processamento
            </summary>
            <param name="file">Arquivo PDF a ser processado</param>
            <returns>Nome do arquivo no Azure Storage</returns>
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.ConsumoPDFController.ProcessarPDF(System.String,System.Boolean)">
            <summary>
            Processa um arquivo PDF já existente no Azure Storage
            </summary>
            <param name="fileName">Nome do arquivo PDF no Azure Storage</param>
            <param name="forceReprocessing">Se deve forçar o reprocessamento mesmo se o arquivo já foi processado</param>
            <returns>Lista de consumos extraídos do PDF</returns>
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.ConsumoPDFController.GetAll">
            <summary>
            Obtém todos os registros de ConsumoPDF
            </summary>
            <returns>Lista de todos os registros de ConsumoPDF</returns>
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.ConsumoPDFController.GetByClienteId(System.Int32)">
            <summary>
            Obtém os registros de ConsumoPDF de um cliente específico
            </summary>
            <param name="clienteId">ID do cliente</param>
            <returns>Lista de registros de ConsumoPDF do cliente</returns>
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.ConsumoPDFController.GetCrayonPDFsRawText">
            <summary>
            Obtém o texto bruto de todos os PDFs Crayon no container Azure
            </summary>
            <returns>Dicionário com nome do arquivo e texto extraído</returns>
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.ConsumoPDFController.GetCrayonPDFsExtractedData">
            <summary>
            Obtém os dados extraídos de todos os PDFs Crayon no container Azure - VERSÃO DE TESTE (SEM PERSISTIR)
            </summary>
            <returns>Dicionário com nome do arquivo e dados extraídos formatados</returns>
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.ConsumoPDFController.TestPDF2023(System.String)">
            <summary>
            Testa extração de um PDF específico de 2023 para debug
            </summary>
            <param name="fileName">Nome do arquivo PDF</param>
            <returns>Dados extraídos do PDF específico</returns>
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.ConsumoPDFController.DebugPeriods(System.String)">
            <summary>
            Debug de períodos extraídos de um PDF específico
            </summary>
            <param name="fileName">Nome do arquivo PDF</param>
            <returns>Informações de debug sobre períodos extraídos</returns>
        </member>
        <member name="T:RnD.BackEnd.API.Controllers.DataEntryController">
            <summary>
            Data entry controller that interacts with DataEntry API
            </summary>
            <seealso cref="T:Microsoft.AspNetCore.Mvc.ControllerBase" />
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.DataEntryController.#ctor(RnD.BackEnd.Domain.Interfaces.Services.IDataEntryService,AutoMapper.IMapper)">
            <summary>
            Initializes a new instance of the <see cref="T:RnD.BackEnd.API.Controllers.DataEntryController"/> class.
            </summary>
            <param name="dataEntryService">The data entry service.</param>
            <param name="mapper">The mapper.</param>
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.DataEntryController.GetDataEntryEntities">
            <summary>
            Gets the data entry entities.
            </summary>
            <returns>
            The list of all data entry entities.
            </returns>
            <response code="200">Returns a List of entities</response>
            <response code="204">No content response</response>
            <response code="500">Internal error</response>
        </member>
        <member name="T:RnD.BackEnd.API.Controllers.DynamicEntityController">
            <summary>
            Dynamic entity controller
            </summary>
            <seealso cref="T:Microsoft.AspNetCore.Mvc.ControllerBase" />
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.DynamicEntityController.#ctor(RnD.BackEnd.Domain.Interfaces.Services.IDynamicEntityService,AutoMapper.IMapper)">
            <summary>
            Initializes a new instance of the <see cref="T:RnD.BackEnd.API.Controllers.DynamicEntityController"/> class.
            </summary>
            <param name="dynamicEntityService">The dynamic entity service.</param>
            <param name="mapper">The mapper.</param>
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.DynamicEntityController.Add(System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            Adds the entity in the request.
            </summary>
            <param name="request">The request.</param>
            <returns>
            The added entity.
            </returns>
            <response code="200">Returns The added entity.</response>
            <response code="400">Malformed request</response>
            <response code="500">Internal error</response>
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.DynamicEntityController.Update(System.Collections.Generic.Dictionary{System.String,System.Object},System.String)">
            <summary>
            Updates the entity in the request.
            </summary>
            <param name="request">The request.</param>
            <param name="etag">The etag.</param>
            <returns>
            The updated entity.
            </returns>
            <response code="200">Returns The updated entity.</response>
            <response code="400">Malformed request</response>
            <response code="412">Error updating entity.</response>
            <response code="500">Internal error</response>
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.DynamicEntityController.Delete(System.String)">
            <summary>
            Deletes an entity by identifier.
            </summary>
            <param name="id">The identifier.</param>
            <returns>
            True if entity was deleted.
            </returns>
            <response code="204">No content response</response>
            <response code="400">Malformed request</response>
            <response code="500">Internal error</response>
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.DynamicEntityController.Get(System.String)">
            <summary>
            Gets an entity by identifier.
            </summary>
            <param name="id">The identifier.</param>
            <returns>
            The entity.
            </returns>
            <response code="200">Returns The entity.</response>
            <response code="204">No content response</response>
            <response code="400">Malformed request</response>
            <response code="500">Internal error</response>
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.DynamicEntityController.List(System.Nullable{System.Int32},System.Nullable{System.Int32},System.String,System.Boolean,System.String,System.Threading.CancellationToken)">
            <summary>
            Lists the entities.
            </summary>
            <param name="pageNumber">The page number.</param>
            <param name="maxItems">The maximum items.</param>
            <param name="sortField">The sort field.</param>
            <param name="sortAscending">if set to <c>true</c> [sort ascending].</param>
            <param name="continuationToken">The continuation token.</param>
            <param name="cancellationToken">The cancellation token.</param>
            <returns>
            The list of entities.
            </returns>
            <response code="200">Returns The list of entities.</response>
            <response code="204">No content response</response>
            <response code="400">Malformed request</response>
            <response code="500">Internal error</response>
        </member>
        <member name="T:RnD.BackEnd.API.Controllers.EmailController">
            <summary>
            Email controller
            </summary>
            <seealso cref="T:Microsoft.AspNetCore.Mvc.ControllerBase" />
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.EmailController.#ctor(RnD.BackEnd.Domain.Interfaces.Services.IEmailService,AutoMapper.IMapper)">
            <summary>
            Initializes a new instance of the <see cref="T:RnD.BackEnd.API.Controllers.EmailController"/> class.
            </summary>
            <param name="emailService">The email service.</param>
            <param name="mapper">The mapper.</param>
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.EmailController.SendCodeAcademyEmail(RnD.BackEnd.API.Model.Email.EmailMessageDto{RnD.BackEnd.API.Model.Email.CodeAcademyTemplateDto})">
            <summary>
            Sends the code academy email.
            </summary>
            <param name="request">The request.</param>
            <returns>
            True if email was sent, False otherwise.
            </returns>
            <response code="200">Returns true if e-mail was sent, otherwise False</response>
            <response code="400">Malformed request</response>
            <response code="500">Internal error</response>
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.EmailController.SendDefaultTemplateEmail(RnD.BackEnd.API.Model.Email.EmailMessageDto{RnD.BackEnd.API.Model.Email.EmailDto})">
            <summary>
            Sends the email with the default template.
            </summary>
            <param name="request">The request.</param>
            <returns>
            True if email was sent, False otherwise.
            </returns>
            <response code="200">Returns true if e-mail was sent, otherwise False</response>
            <response code="400">Malformed request</response>
            <response code="500">Internal error</response>
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.EmailController.SendTemplatedEmail(RnD.BackEnd.API.Model.Email.SpecificTemplateDto)">
            <summary>
            Sends the email with a specific template Id.
            </summary>
            <param name="request">The request.</param>
            <returns>
            True if email was sent, False otherwise.
            </returns>
            <response code="200">Returns true if e-mail was sent, otherwise False</response>
            <response code="400">Malformed request</response>
            <response code="500">Internal error</response>
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.EmailController.SendDefaultTemplateEmailWithAttachments(RnD.BackEnd.API.Model.Email.EmailAttachmentsDto{RnD.BackEnd.API.Model.Email.EmailDto})">
            <summary>
            Sends the default template email with attachments.
            </summary>
            <param name="request">The request.</param>
            <returns>
            True if email was sent, False otherwise.
            </returns>
            <response code="200">Returns true if e-mail was sent, otherwise False</response>
            <response code="400">Malformed request</response>
            <response code="500">Internal error</response>
        </member>
        <member name="T:RnD.BackEnd.API.Controllers.VehicleController">
            <summary>
            Vehicle controller
            </summary>
            <seealso cref="T:Microsoft.AspNetCore.Mvc.ControllerBase" />
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.VehicleController.#ctor(RnD.BackEnd.Domain.Interfaces.Services.IVehiclesService,AutoMapper.IMapper)">
            <summary>
            Initializes a new instance of the <see cref="T:RnD.BackEnd.API.Controllers.VehicleController"/> class.
            </summary>
            <param name="vehiclesService">The vehicles service.</param>
            <param name="mapper">The mapper.</param>
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.VehicleController.AddAsync(RnD.BackEnd.API.Model.Vehicle.CreateVehicleDto)">
            <summary>
            Creates a new vehicle
            </summary>
            <param name="request">The new vehicle object</param>
            <returns>
            The newly created vehicle
            </returns>
            <response code="201">Returns the newly created vehicle</response>
            <response code="400">Malformed request</response>
            <response code="500">Internal error</response>
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.VehicleController.AddManyAsync(System.Collections.Generic.List{RnD.BackEnd.API.Model.Vehicle.CreateVehicleDto})">
            <summary>
            Creates many new vehicles
            Caution: Only objects within the same partition may be created
            IF UNSURE DO NOT USE
            </summary>
            <param name="request">The list of new vehicle objects</param>
            <returns>The newly created vehicles</returns>
            <response code="201">Returns the newly created vehicles</response>
            <response code="400">Malformed request</response>
            <response code="500">Internal error</response>
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.VehicleController.AddOrUpdateAsync(RnD.BackEnd.API.Model.Vehicle.VehicleDto)">
            <summary>
            Adds or updates a vehicle
            Caution: If the ID is not found, it will create a new object
            It is safer and more canonical to use separate endpoints
            </summary>
            <param name="request">The vehicle</param>
            <returns>The vehicle</returns>
            <response code="200">Returns the newly created vehicles</response>
            <response code="400">Malformed request</response>
            <response code="500">Internal error</response>
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.VehicleController.UpdateAsync(RnD.BackEnd.API.Model.Vehicle.VehicleDto,System.String,System.String)">
            <summary>
            Updates a vehicle
            </summary>
            <param name="request">The vehicle to update</param>
            <param name="id">The vehicle identifier</param>
            <param name="etag">The latest write tag (in concurrency cases, to avoid overwriting the existing document)</param>
            <returns>
            The updated vehicle
            </returns>
            <response code="200">Returns the newly created vehicles</response>
            <response code="400">Malformed request</response>
            <response code="404">Vehicle to update not found</response>
            <response code="409">Vehicle to update changed in another context</response>
            <response code="500">Internal error</response>
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.VehicleController.PatchAsync(System.String,Microsoft.AspNetCore.JsonPatch.JsonPatchDocument{RnD.BackEnd.API.Model.Vehicle.VehicleDto})">
            <summary>
            Updates a vehicle
            </summary>
            <param name="id">The vehicle identifier</param>
            <param name="request">The changes</param>
            <returns>
            The updated vehicle
            </returns>
            <response code="200">Returns the updated vehicle</response>
            <response code="400">Malformed request</response>
            <response code="404">Vehicle to patch not found</response>
            <response code="500">Internal error</response>
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.VehicleController.DeleteAsync(System.String)">
            <summary>
            Deletes a vehicle
            </summary>
            <param name="id">The vehicle identifier</param>
            <returns></returns>
            <response code="204">Vehicle deleted</response>
            <response code="400">Malformed request</response>
            <response code="500">Internal error</response>
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.VehicleController.GetAsync(System.String)">
            <summary>
            Gets a vehicle by ID
            </summary>
            <param name="id">The vehicle identifier</param>
            <returns>The vehicle</returns>
            <response code="200">Returns a vehicle</response>
            <response code="400">Malformed request</response>
            <response code="404">Vehicle not found</response>
            <response code="500">Internal error</response>
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.VehicleController.ListAsync(System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.Int32},System.Nullable{System.Int32},System.Nullable{System.Int32},System.String,System.Boolean,System.String,System.Threading.CancellationToken)">
            <summary>
            Gets all vehicles with custom filters
            </summary>
            <param name="id">Vehicle identifiers</param>
            <param name="brandId">Vehicle brands</param>
            <param name="modelName">Vehicle models</param>
            <param name="fuelType">Vehicle fuel types</param>
            <param name="version">Vehicle versions</param>
            <param name="year">Vehicle production year</param>
            <param name="pageNumber">The page number.</param>
            <param name="maxItems">The maximum number of items to be returned per request (may return a token to get the next page of results)</param>
            <param name="sortField">The sort field.</param>
            <param name="sortAscending">if set to <c>true</c> [sort ascending].</param>
            <param name="continuationToken">The token to present the next page of results (URLEncoded)</param>
            <param name="cancellationToken">The cancellation token.</param>
            <returns>
            A list of vehicles
            </returns>
            <response code="200">Returns a list of vehicles</response>
            <response code="400">Malformed request</response>
            <response code="500">Internal error</response>
        </member>
        <member name="T:RnD.BackEnd.API.Controllers.WeatherForecastController">
            <summary>
            Weather forecast controller
            </summary>
            <seealso cref="T:Microsoft.AspNetCore.Mvc.ControllerBase" />
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.WeatherForecastController.#ctor(RnD.BackEnd.Domain.Interfaces.Services.IWeatherService,AutoMapper.IMapper)">
            <summary>
            Initializes a new instance of the <see cref="T:RnD.BackEnd.API.Controllers.WeatherForecastController"/> class.
            </summary>
            <param name="weatherService">The weather service.</param>
            <param name="mapper">The mapper.</param>
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.WeatherForecastController.AddAsync(RnD.BackEnd.API.Model.Weather.CreateWeatherDto)">
            <summary>
            Creates a new weather
            </summary>
            <param name="request">The new weather object</param>
            <returns>The newly created weather</returns>
            <response code="201">Returns the newly created weather</response>
            <response code="400">Malformed request</response>
            <response code="500">Internal error</response>
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.WeatherForecastController.AddWithTransactionSample">
            <summary>
            Test UoW transaction
            </summary>
            <returns>
            The newly created weathers
            </returns>
            <response code="201">Returns the newly created weathers</response>
            <response code="400">Malformed request</response>
            <response code="500">Internal error</response>
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.WeatherForecastController.UpdateAsync(RnD.BackEnd.API.Model.Weather.WeatherDto,System.Guid,System.String)">
            <summary>
            Updates a weather
            </summary>
            <param name="request">The weather to update</param>
            <param name="id">The weather identifier</param>
            <param name="rowversion">The latest write tag (in concurrency cases, to avoid overwriting the existing document)</param>
            <returns>The updated weather</returns>
            <response code="200">Returns the newly created weathers</response>
            <response code="400">Malformed request</response>
            <response code="404">Weather not found</response>
            <response code="409">Conflict on the request</response>
            <response code="500">Internal error</response>
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.WeatherForecastController.PatchAsync(System.Guid,Microsoft.AspNetCore.JsonPatch.JsonPatchDocument{RnD.BackEnd.API.Model.Weather.WeatherDto})">
            <summary>
            Updates a weather
            </summary>
            <param name="id">The weather identifier</param>
            <param name="request">The changes</param>
            <returns>
            The updated weather
            </returns>
            <response code="200">Returns the updated weather</response>
            <response code="400">Malformed request</response>
            <response code="404">Weather not found</response>
            <response code="500">Internal error</response>
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.WeatherForecastController.DeleteAsync(System.Guid)">
            <summary>
            Deletes a weather
            </summary>
            <param name="id">The weather identifier</param>
            <returns></returns>
            <response code="204">The weather was deleted</response>
            <response code="400">Malformed request</response>
            <response code="500">Internal error</response>
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.WeatherForecastController.GetAsync(System.Guid)">
            <summary>
            Gets a weather by ID
            </summary>
            <param name="id">The weather identifier</param>
            <returns>The weather</returns>
            <response code="200">Returns a weather</response>
            <response code="400">Malformed request</response>
            <response code="404">Weather not found</response>
            <response code="500">Internal error</response>
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.WeatherForecastController.ListAsync(System.Guid,System.String,System.Nullable{System.Int32},System.Nullable{System.Int32},System.String,System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Gets all weathers with custom filters
            </summary>
            <param name="id">Weather identifier</param>
            <param name="location">Location</param>
            <param name="itemsPerPage">The number of items per page.</param>
            <param name="page">The page.</param>
            <param name="sortField">The sort field.</param>
            <param name="sortAscending">if set to <c>true</c> [sort ascending].</param>
            <param name="cancellationToken">The cancellation token.</param>
            <returns>
            A list of weather forecasts
            </returns>
            <response code="200">Returns a list of weathers</response>
            <response code="400">Malformed request</response>
            <response code="404">Weather not found</response>
            <response code="500">Internal error</response>
        </member>
        <member name="T:RnD.BackEnd.API.Controllers.WorkflowController">
            <summary>
            Workflow controller that interacts with WFE API, for demonstration purpose
            </summary>
            <seealso cref="T:Microsoft.AspNetCore.Mvc.ControllerBase" />
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.WorkflowController.#ctor(RnD.BackEnd.Domain.Interfaces.Services.IWorkflowService,AutoMapper.IMapper)">
            <summary>
            Initializes a new instance of the <see cref="T:RnD.BackEnd.API.Controllers.WorkflowController"/> class.
            </summary>
            <param name="workflowService">The workflow service.</param>
            <param name="mapper">The mapper.</param>
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.WorkflowController.GetDefinitions(RnD.BackEnd.API.Model.Workflow.GetDefinitionsDto)">
            <summary>
            Gets the workflow definitions.
            </summary>
            <param name="request">The request.</param>
            <returns>
            The list of workflow definitions.
            </returns>
            <response code="200">Returns a List of definitions</response>
            <response code="204">No content response</response>
            <response code="400">Malformed request</response>
            <response code="401">User Unauthorized</response>
            <response code="500">Internal error</response>
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.WorkflowController.GetInstances(RnD.BackEnd.API.Model.Workflow.WorkflowInstanceBySourceIdsDto)">
            <summary>
            Gets the list of workflow instances by a list of sourceIds and/or by a list of status.
            </summary>
            <param name="request">The request.</param>
            <returns>
            The list of workflow instances for the source identifier.
            </returns>
            <response code="200">Returns a List of Instances by a list of sourceIds and/or by a list of status</response>
            <response code="204">No content response</response>
            <response code="400">Malformed request</response>
            <response code="401">User Unauthorized</response>
            <response code="500">Internal error</response>
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.WorkflowController.GetUserInstances(RnD.BackEnd.API.Model.Workflow.GetUserInstancesDto)">
            <summary>
            Gets the list of workflow instances initiated by current user.
            </summary>
            <param name="request">The request.</param>
            <returns>
            The list of workflow instances.
            </returns>
            <response code="200">Returns a list of User Instances</response>
            <response code="204">No content response</response>
            <response code="401">User Unauthorized</response>
            <response code="500">Internal error</response>
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.WorkflowController.GetInstance(System.String)">
            <summary>
            Gets the workflow instance by identifier.
            </summary>
            <param name="id">The identifier.</param>
            <returns>
            The workflow instance.
            </returns>
            <response code="200">Returns a Instance by Id</response>
            <response code="204">No content response</response>
            <response code="400">Malformed request</response>
            <response code="401">User Unauthorized</response>
            <response code="500">Internal error</response>
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.WorkflowController.CancelWorkflow(System.Guid,System.String,RnD.BackEnd.API.Model.Workflow.WorkflowEmailDto)">
            <summary>
            Cancels the workflow instance.
            </summary>
            <param name="instanceId">The instance identifier.</param>
            <param name="userId">The user identifier.</param>
            <param name="request">The request.</param>
            <returns>
            True if workflow instance was cancelled, False otherwise.
            </returns>
            <response code="200">Returns True if sucess, false otherwise</response>
            <response code="400">Malformed request</response>
            <response code="401">User Unauthorized</response>
            <response code="500">Internal error</response>
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.WorkflowController.DeleteOrCancelWorkflowsBySourceId(System.String,RnD.BackEnd.API.Model.Workflow.WorkflowEmailDto,System.Boolean)">
            <summary>
            Deletes the or cancel workflows by source identifier.
            </summary>
            <param name="sourceId">The source identifier.</param>
            <param name="request">The request.</param>
            <param name="isDeleteOperation">if set to <c>true</c> [is delete operation].</param>
            <returns>
            True if operation succeeded, False otherwise.
            </returns>
            <response code="200">Returns True if sucess, false otherwise</response>
            <response code="204">No content response</response>
            <response code="401">User Unauthorized</response>
            <response code="500">Internal error</response>
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.WorkflowController.GetTask(System.String)">
            <summary>
            Gets the workflow task by identifier.
            </summary>
            <param name="id">The identifier.</param>
            <returns>
            The workflow task.
            </returns>
            <response code="200">Returns Task by Id</response>
            <response code="204">No content response</response>
            <response code="400">Malformed request</response>
            <response code="401">User Unauthorized</response>
            <response code="500">Internal error</response>
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.WorkflowController.GetTasks(RnD.BackEnd.API.Model.Workflow.GetTasksDto)">
            <summary>
            Gets the list of workflow tasks.
            </summary>
            <param name="request">The request.</param>
            <returns>
            The list of workflow tasks.
            </returns>
            <response code="200">Returns a list of Tasks</response>
            <response code="204">No content response</response>
            <response code="401">User Unauthorized</response>
            <response code="500">Internal error</response>
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.WorkflowController.UpdateTask(RnD.BackEnd.API.Model.Workflow.UpdateTaskDto)">
            <summary>
            Updates the task.
            </summary>
            <param name="request">The request.</param>
            <returns></returns>
            <response code="200">Returns True if sucess, false otherwise</response>
            <response code="400">Malformed request</response>
            <response code="401">User Unauthorized</response>
            <response code="500">Internal error</response>
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.WorkflowController.UpdateTasks(System.Collections.Generic.List{RnD.BackEnd.API.Model.Workflow.UpdateTaskDto})">
            <summary>
            Updates multiple tasks.
            </summary>
            <param name="request">The request.</param>
            <returns>
            True if operations succeeded, False otherwise.
            </returns>
            <response code="200">Returns the update output for each task</response>
            <response code="400">Malformed request</response>
            <response code="401">User Unauthorized</response>
            <response code="500">Internal error</response>
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.WorkflowController.ReassingTask(RnD.BackEnd.API.Model.Workflow.ReassignTaskDto)">
            <summary>
            Reassings the task.
            </summary>
            <param name="request">The request.</param>
            <returns>
            True if task was reassigned, False otherwise.
            </returns>
            <response code="200">Returns True if sucess, false otherwise</response>
            <response code="400">Malformed request</response>
            <response code="401">User Unauthorized</response>
            <response code="500">Internal error</response>
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.WorkflowController.StartWorkflow(System.Collections.Generic.List{RnD.BackEnd.API.Model.Workflow.StartWorkflowDto})">
            <summary>
            Starts the workflow.
            </summary>
            <param name="request">The request.</param>
            <returns>
            The workflow instance that was started.
            </returns>
            <response code="200">Returns the workflow that was started</response>
            <response code="400">Malformed request</response>
            <response code="401">User Unauthorized</response>
            <response code="500">Internal error</response>
        </member>
        <member name="M:RnD.BackEnd.API.Controllers.WorkflowController.TriggerAutomaticWorkflow(RnD.BackEnd.API.Model.Workflow.StartAutomaticWorkflowDto)">
            <summary>
            Triggers the automatic workflow.
            </summary>
            <param name="request">The request.</param>
            <returns>
            The workflow instance that was started.
            </returns>
            <response code="200">Returns the workflow that was Automatic started</response>
            <response code="400">Malformed request</response>
            <response code="401">User Unauthorized</response>
            <response code="500">Internal error</response>
        </member>
        <member name="T:RnD.BackEnd.API.Extensions.ServiceCollectionExtensions">
            <summary>
            Service collection extensions class
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.Extensions.ServiceCollectionExtensions.ConfigureInfrastructure(Microsoft.Extensions.DependencyInjection.IServiceCollection,RnD.BackEnd.Domain.Settings.AppSettings)">
            <summary>
            Configures the infrastructure layer.
            </summary>
            <param name="services">The services.</param>
            <param name="appSettings">The application settings.</param>
            <returns>
            The service collection.
            </returns>
            <exception cref="T:System.Exception">No appsettings section has been found
            or
            No valid settings.</exception>
        </member>
        <member name="M:RnD.BackEnd.API.Extensions.ServiceCollectionExtensions.ConfigureBusinessServices(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Configures the business services.
            </summary>
            <param name="services">The services.</param>
            <returns>
            The service collection.
            </returns>
        </member>
        <member name="M:RnD.BackEnd.API.Extensions.ServiceCollectionExtensions.ConfigurePolicies(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Configures the policies.
            </summary>
            <param name="services">The services.</param>
            <returns>
            The service collection.
            </returns>
        </member>
        <member name="T:RnD.BackEnd.API.Helpers.EnumSchemaFilter">
            <summary>
            Enum to string for usage in swagger documentation
            </summary>
            <seealso cref="T:Swashbuckle.AspNetCore.SwaggerGen.ISchemaFilter" />
        </member>
        <member name="M:RnD.BackEnd.API.Helpers.EnumSchemaFilter.Apply(Microsoft.OpenApi.Models.OpenApiSchema,Swashbuckle.AspNetCore.SwaggerGen.SchemaFilterContext)">
            <summary>
            Applies the specified schema.
            </summary>
            <param name="schema">The schema.</param>
            <param name="context">The context.</param>
        </member>
        <member name="T:RnD.BackEnd.API.Helpers.LocalizationMiddleware">
            <summary>
            Localization middleware class
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.Helpers.LocalizationMiddleware.#ctor(Microsoft.AspNetCore.Http.RequestDelegate)">
            <summary>
            Initializes a new instance of the <see cref="T:RnD.BackEnd.API.Helpers.LocalizationMiddleware" /> class.
            </summary>
            <param name="next">The next.</param>
            <exception cref="T:System.ArgumentNullException">next</exception>
        </member>
        <member name="M:RnD.BackEnd.API.Helpers.LocalizationMiddleware.Invoke(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            Invokes the specified HTTP context.
            </summary>
            <param name="httpContext">The HTTP context.</param>
            <exception cref="T:System.ArgumentNullException">httpContext</exception>
        </member>
        <member name="T:RnD.BackEnd.API.Helpers.WorkflowEngineAllowedAttribute">
            <summary>
            Action filter Attribute to check if workflow engine is allowed
            </summary>
            <seealso cref="T:Microsoft.AspNetCore.Mvc.Filters.ActionFilterAttribute" />
        </member>
        <member name="M:RnD.BackEnd.API.Helpers.WorkflowEngineAllowedAttribute.#ctor(AutoMapper.IMapper,Microsoft.Extensions.Options.IOptions{RnD.BackEnd.Domain.Settings.AppSettings})">
            <summary>
            Initializes a new instance of the <see cref="T:RnD.BackEnd.API.Helpers.WorkflowEngineAllowedAttribute" /> class.
            </summary>
            <param name="mapper">The mapper.</param>
            <param name="settings">The settings.</param>
        </member>
        <member name="M:RnD.BackEnd.API.Helpers.WorkflowEngineAllowedAttribute.OnActionExecutionAsync(Microsoft.AspNetCore.Mvc.Filters.ActionExecutingContext,Microsoft.AspNetCore.Mvc.Filters.ActionExecutionDelegate)">
            <summary>
            Intercept a request to validate if workflow engine is allowed
            </summary>
            <param name="context"></param>
            <param name="next"></param>
            <inheritdoc />
        </member>
        <member name="T:RnD.BackEnd.API.MappingProfiles.DataEntryProfile">
            <summary>
            Data entry mapping profile
            </summary>
            <seealso cref="T:AutoMapper.Profile" />
        </member>
        <member name="M:RnD.BackEnd.API.MappingProfiles.DataEntryProfile.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:RnD.BackEnd.API.MappingProfiles.DataEntryProfile"/> class.
            </summary>
        </member>
        <member name="T:RnD.BackEnd.API.MappingProfiles.EmailProfile">
            <summary>
            Email mapping profile
            </summary>
            <seealso cref="T:AutoMapper.Profile" />
        </member>
        <member name="M:RnD.BackEnd.API.MappingProfiles.EmailProfile.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:RnD.BackEnd.API.MappingProfiles.EmailProfile"/> class.
            </summary>
        </member>
        <member name="T:RnD.BackEnd.API.MappingProfiles.GenericProfile">
            <summary>
            Generic mapping profile
            </summary>
            <seealso cref="T:AutoMapper.Profile" />
        </member>
        <member name="M:RnD.BackEnd.API.MappingProfiles.GenericProfile.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:RnD.BackEnd.API.MappingProfiles.GenericProfile"/> class.
            </summary>
        </member>
        <member name="T:RnD.BackEnd.API.MappingProfiles.VehicleProfile">
            <summary>
            Vehicle mapping profile
            </summary>
            <seealso cref="T:AutoMapper.Profile" />
        </member>
        <member name="M:RnD.BackEnd.API.MappingProfiles.VehicleProfile.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:RnD.BackEnd.API.MappingProfiles.VehicleProfile"/> class.
            </summary>
        </member>
        <member name="T:RnD.BackEnd.API.MappingProfiles.WeatherProfile">
            <summary>
            Weather mapping profile
            </summary>
            <seealso cref="T:AutoMapper.Profile" />
        </member>
        <member name="M:RnD.BackEnd.API.MappingProfiles.WeatherProfile.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:RnD.BackEnd.API.MappingProfiles.WeatherProfile"/> class.
            </summary>
        </member>
        <member name="T:RnD.BackEnd.API.MappingProfiles.WorkflowProfile">
            <summary>
            Workflow mapping profile
            </summary>
            <seealso cref="T:AutoMapper.Profile" />
        </member>
        <member name="M:RnD.BackEnd.API.MappingProfiles.WorkflowProfile.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:RnD.BackEnd.API.MappingProfiles.WorkflowProfile"/> class.
            </summary>
        </member>
        <member name="T:RnD.BackEnd.API.Models.CrayonExtractedData">
            <summary>
            Modelo para dados extraídos de PDFs Crayon para testes (sem persistência na BD)
            </summary>
        </member>
        <member name="P:RnD.BackEnd.API.Models.CrayonExtractedData.Cliente">
            <summary>
            Nome do cliente (normalmente "BI4ALL Consultores de Gestão, Lda")
            </summary>
        </member>
        <member name="P:RnD.BackEnd.API.Models.CrayonExtractedData.Descricao">
            <summary>
            Descrição/código do serviço ou produto
            </summary>
        </member>
        <member name="P:RnD.BackEnd.API.Models.CrayonExtractedData.Preco">
            <summary>
            Preço do serviço/produto
            </summary>
        </member>
        <member name="P:RnD.BackEnd.API.Models.CrayonExtractedData.Iva">
            <summary>
            Valor do IVA
            </summary>
        </member>
        <member name="P:RnD.BackEnd.API.Models.CrayonExtractedData.PeriodoInicio">
            <summary>
            Data de início do período de consumo
            </summary>
        </member>
        <member name="P:RnD.BackEnd.API.Models.CrayonExtractedData.PeriodoFim">
            <summary>
            Data de fim do período de consumo
            </summary>
        </member>
        <member name="P:RnD.BackEnd.API.Models.CrayonExtractedData.ExtraidoComSucesso">
            <summary>
            Indica se a extração foi feita com sucesso
            </summary>
        </member>
        <member name="P:RnD.BackEnd.API.Models.CrayonExtractedData.MensagemErro">
            <summary>
            Mensagem de erro, caso a extração tenha falhado
            </summary>
        </member>
        <member name="T:RnD.BackEnd.API.Models.CrayonPDFExtractionResult">
            <summary>
            Resultado da extração de um PDF Crayon
            </summary>
        </member>
        <member name="P:RnD.BackEnd.API.Models.CrayonPDFExtractionResult.NomeArquivo">
            <summary>
            Nome do arquivo PDF
            </summary>
        </member>
        <member name="P:RnD.BackEnd.API.Models.CrayonPDFExtractionResult.IsCrayonPDF">
            <summary>
            Indica se o PDF foi identificado como Crayon
            </summary>
        </member>
        <member name="P:RnD.BackEnd.API.Models.CrayonPDFExtractionResult.TextoBruto">
            <summary>
            Texto bruto extraído do PDF
            </summary>
        </member>
        <member name="P:RnD.BackEnd.API.Models.CrayonPDFExtractionResult.DadosExtraidos">
            <summary>
            Lista de dados extraídos (pode haver múltiplos produtos/serviços por PDF)
            </summary>
        </member>
        <member name="P:RnD.BackEnd.API.Models.CrayonPDFExtractionResult.TemErro">
            <summary>
            Indica se houve erro na extração
            </summary>
        </member>
        <member name="P:RnD.BackEnd.API.Models.CrayonPDFExtractionResult.MensagemErro">
            <summary>
            Mensagem de erro geral
            </summary>
        </member>
        <member name="T:RnD.BackEnd.API.Model.DataEntry.EntityDto">
            <summary>
            Data Entry entity DTO class
            </summary>
        </member>
        <member name="P:RnD.BackEnd.API.Model.DataEntry.EntityDto.Id">
            <summary>
            Gets or sets the identifier.
            </summary>
            <value>
            The identifier.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.DataEntry.EntityDto.ModelId">
            <summary>
            Gets or sets the model identifier.
            </summary>
            <value>
            The model identifier.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.DataEntry.EntityDto.ModelName">
            <summary>
            Gets or sets the name of the model.
            </summary>
            <value>
            The name of the model.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.DataEntry.EntityDto.FunctionalName">
            <summary>
            Gets or sets the name of the functional.
            </summary>
            <value>
            The name of the functional.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.DataEntry.EntityDto.TechnicalName">
            <summary>
            Gets or sets the name of the technical.
            </summary>
            <value>
            The name of the technical.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.DataEntry.EntityDto.DataLength">
            <summary>
            Gets or sets the length of the data.
            </summary>
            <value>
            The length of the data.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.DataEntry.EntityDto.Created">
            <summary>
            Gets or sets the created.
            </summary>
            <value>
            The created.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.DataEntry.EntityDto.CreatedBy">
            <summary>
            Gets or sets the created by.
            </summary>
            <value>
            The created by.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.DataEntry.EntityDto.CreatedByName">
            <summary>
            Gets or sets the name of the created by.
            </summary>
            <value>
            The name of the created by.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.DataEntry.EntityDto.Modified">
            <summary>
            Gets or sets the modified.
            </summary>
            <value>
            The modified.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.DataEntry.EntityDto.ModifiedBy">
            <summary>
            Gets or sets the modified by.
            </summary>
            <value>
            The modified by.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.DataEntry.EntityDto.ModifiedByName">
            <summary>
            Gets or sets the name of the modified by.
            </summary>
            <value>
            The name of the modified by.
            </value>
        </member>
        <member name="T:RnD.BackEnd.API.Model.DataEntry.EntityListDto">
            <summary>
            Data Entry entity list DTO class
            </summary>
        </member>
        <member name="P:RnD.BackEnd.API.Model.DataEntry.EntityListDto.Entities">
            <summary>
            Gets or sets the entities.
            </summary>
            <value>
            The entities.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.DataEntry.EntityListDto.TotalRows">
            <summary>
            Gets or sets the total rows.
            </summary>
            <value>
            The total rows.
            </value>
        </member>
        <member name="T:RnD.BackEnd.API.Model.Email.CodeAcademyTemplateDto">
            <summary>
            Code academy email template DTO class
            </summary>
            <seealso cref="T:RnD.BackEnd.API.Model.Email.EmailDto" />
        </member>
        <member name="P:RnD.BackEnd.API.Model.Email.CodeAcademyTemplateDto.Session">
            <summary>
            Gets or sets the session.
            </summary>
            <value>
            The session.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Email.CodeAcademyTemplateDto.Date">
            <summary>
            Gets or sets the date.
            </summary>
            <value>
            The date.
            </value>
        </member>
        <member name="T:RnD.BackEnd.API.Model.Email.EmailAttachmentsDto`1">
            <summary>
            Email with attachments DTO class
            </summary>
            <typeparam name="T"></typeparam>
            <seealso cref="T:RnD.BackEnd.API.Model.Email.EmailMessageDto`1" />
        </member>
        <member name="P:RnD.BackEnd.API.Model.Email.EmailAttachmentsDto`1.Attachments">
            <summary>
            Gets or sets the attachments.
            </summary>
            <value>
            The attachments.
            </value>
        </member>
        <member name="T:RnD.BackEnd.API.Model.Email.EmailDto">
            <summary>
            Email DTO class
            </summary>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Email.EmailDto.Message">
            <summary>
            Gets or sets the message.
            </summary>
            <value>
            The message.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Email.EmailDto.Subject">
            <summary>
            Gets or sets the subject.
            </summary>
            <value>
            The subject.
            </value>
        </member>
        <member name="T:RnD.BackEnd.API.Model.Email.EmailMessageDto`1">
            <summary>
            Email message DTO class
            </summary>
            <typeparam name="T"></typeparam>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Email.EmailMessageDto`1.TemplateData">
            <summary>
            Gets or sets the template data.
            </summary>
            <value>
            The template data.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Email.EmailMessageDto`1.Recipients">
            <summary>
            Gets or sets the recipients.
            </summary>
            <value>
            The recipients.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Email.EmailMessageDto`1.RecipientsCC">
            <summary>
            Gets or sets the recipients cc.
            </summary>
            <value>
            The recipients cc.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Email.EmailMessageDto`1.RecipientsBCC">
            <summary>
            Gets or sets the recipients BCC.
            </summary>
            <value>
            The recipients BCC.
            </value>
        </member>
        <member name="T:RnD.BackEnd.API.Model.Email.SpecificTemplateDto">
            <summary>
            Specific template identifier DTO class
            </summary>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Email.SpecificTemplateDto.TemplateId">
            <summary>
            Gets or sets the template identifier.
            </summary>
            <value>
            The template identifier.
            </value>
        </member>
        <member name="T:RnD.BackEnd.API.Model.Exception.ModelException">
            <summary>
            Model exception class
            </summary>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Exception.ModelException.Messages">
            <summary>
            Gets or sets the messages.
            </summary>
            <value>
            The messages.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Exception.ModelException.HasMessages">
            <summary>
            Gets a value indicating whether this instance has messages.
            </summary>
            <value>
              <c>true</c> if this instance has messages; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="M:RnD.BackEnd.API.Model.Exception.ModelException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:RnD.BackEnd.API.Model.Exception.ModelException"/> class.
            </summary>
        </member>
        <member name="T:RnD.BackEnd.API.Model.Generic.ApiOutput`1">
            <summary>
            API output class
            </summary>
            <typeparam name="T"></typeparam>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Generic.ApiOutput`1.Code">
            <summary>
            Gets or sets the code.
            </summary>
            <value>
            The code.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Generic.ApiOutput`1.Description">
            <summary>
            Gets or sets the description.
            </summary>
            <value>
            The description.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Generic.ApiOutput`1.Value">
            <summary>
            Gets or sets the value.
            </summary>
            <value>
            The value.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Generic.ApiOutput`1.Error">
            <summary>
            Gets or sets a value indicating whether this <see cref="T:RnD.BackEnd.API.Model.Generic.ApiOutput`1"/> is error.
            </summary>
            <value>
              <c>true</c> if error; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Generic.ApiOutput`1.ExceptionMessages">
            <summary>
            Gets or sets the exception messages.
            </summary>
            <value>
            The exception messages.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Generic.ApiOutput`1.Properties">
            <summary>
            Gets or sets the properties.
            </summary>
            <value>
            The properties.
            </value>
        </member>
        <member name="M:RnD.BackEnd.API.Model.Generic.ApiOutput`1.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:RnD.BackEnd.API.Model.Generic.ApiOutput`1"/> class.
            </summary>
        </member>
        <member name="T:RnD.BackEnd.API.Model.Vehicle.CreateVehicleDto">
            <summary>
            Create vehicle DTO class
            </summary>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Vehicle.CreateVehicleDto.BrandId">
            <summary>
            Gets or sets the brand identifier.
            </summary>
            <value>
            The brand identifier.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Vehicle.CreateVehicleDto.Brand">
            <summary>
            Gets or sets the brand.
            </summary>
            <value>
            The brand.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Vehicle.CreateVehicleDto.ModelName">
            <summary>
            Gets or sets the name of the model.
            </summary>
            <value>
            The name of the model.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Vehicle.CreateVehicleDto.FuelType">
            <summary>
            Gets or sets the type of the fuel.
            </summary>
            <value>
            The type of the fuel.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Vehicle.CreateVehicleDto.Version">
            <summary>
            Gets or sets the version.
            </summary>
            <value>
            The version.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Vehicle.CreateVehicleDto.Year">
            <summary>
            Gets or sets the year.
            </summary>
            <value>
            The year.
            </value>
        </member>
        <member name="T:RnD.BackEnd.API.Model.Vehicle.VehicleDto">
            <summary>
            Vehicle DTO class
            </summary>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Vehicle.VehicleDto.Id">
            <summary>
            Gets or sets the identifier.
            </summary>
            <value>
            The identifier.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Vehicle.VehicleDto.BrandId">
            <summary>
            Gets or sets the brand identifier.
            </summary>
            <value>
            The brand identifier.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Vehicle.VehicleDto.Brand">
            <summary>
            Gets or sets the brand.
            </summary>
            <value>
            The brand.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Vehicle.VehicleDto.ModelName">
            <summary>
            Gets or sets the name of the model.
            </summary>
            <value>
            The name of the model.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Vehicle.VehicleDto.FuelType">
            <summary>
            Gets or sets the type of the fuel.
            </summary>
            <value>
            The type of the fuel.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Vehicle.VehicleDto.Version">
            <summary>
            Gets or sets the version.
            </summary>
            <value>
            The version.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Vehicle.VehicleDto.Year">
            <summary>
            Gets or sets the year.
            </summary>
            <value>
            The year.
            </value>
        </member>
        <member name="T:RnD.BackEnd.API.Model.Weather.CreateWeatherDto">
            <summary>
            Create weather DTO class
            </summary>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Weather.CreateWeatherDto.Temperature">
            <summary>
            Gets or sets the temperature.
            </summary>
            <value>
            The temperature.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Weather.CreateWeatherDto.Summary">
            <summary>
            Gets or sets the summary.
            </summary>
            <value>
            The summary.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Weather.CreateWeatherDto.Location">
            <summary>
            Gets or sets the location.
            </summary>
            <value>
            The location.
            </value>
        </member>
        <member name="T:RnD.BackEnd.API.Model.Weather.WeatherDto">
            <summary>
            Weather DTO class
            </summary>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Weather.WeatherDto.Id">
            <summary>
            Gets or sets the identifier.
            </summary>
            <value>
            The identifier.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Weather.WeatherDto.Temperature">
            <summary>
            Gets or sets the temperature.
            </summary>
            <value>
            The temperature.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Weather.WeatherDto.Summary">
            <summary>
            Gets or sets the summary.
            </summary>
            <value>
            The summary.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Weather.WeatherDto.Location">
            <summary>
            Gets or sets the location.
            </summary>
            <value>
            The location.
            </value>
        </member>
        <member name="T:RnD.BackEnd.API.Model.Workflow.CustomStepDto">
            <summary>
            Custom step request DTO class
            </summary>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.CustomStepDto.StepUsers">
            <summary>
            Gets or sets the step users.
            </summary>
            <value>
            The step users.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.CustomStepDto.RequireAllToApprove">
            <summary>
            Gets or sets a value indicating whether [require all to approve].
            </summary>
            <value>
              <c>true</c> if [require all to approve]; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.CustomStepDto.SkipOverOtherTasks">
            <summary>
            Gets or sets a value indicating whether [skip over other tasks].
            </summary>
            <value>
              <c>true</c> if [skip over other tasks]; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.CustomStepDto.StepName">
            <summary>
            Gets or sets the name of the step.
            </summary>
            <value>
            The name of the step.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.CustomStepDto.Order">
            <summary>
            Gets or sets the order.
            </summary>
            <value>
            The order.
            </value>
        </member>
        <member name="T:RnD.BackEnd.API.Model.Workflow.Enums.TaskStatus">
            <summary>
            Task status possible for Workflow DTO enum
            </summary>
        </member>
        <member name="F:RnD.BackEnd.API.Model.Workflow.Enums.TaskStatus.TODO">
            <summary>
            The todo
            </summary>
        </member>
        <member name="F:RnD.BackEnd.API.Model.Workflow.Enums.TaskStatus.CANCELLED">
            <summary>
            The cancelled
            </summary>
        </member>
        <member name="F:RnD.BackEnd.API.Model.Workflow.Enums.TaskStatus.APPROVED">
            <summary>
            The approved
            </summary>
        </member>
        <member name="F:RnD.BackEnd.API.Model.Workflow.Enums.TaskStatus.REJECTED">
            <summary>
            The rejected
            </summary>
        </member>
        <member name="F:RnD.BackEnd.API.Model.Workflow.Enums.TaskStatus.FROWARDED">
            <summary>
            The frowarded
            </summary>
        </member>
        <member name="T:RnD.BackEnd.API.Model.Workflow.Enums.WorkflowPurpose">
            <summary>
            Purpose possible for Workflow DTO enum
            </summary>
        </member>
        <member name="F:RnD.BackEnd.API.Model.Workflow.Enums.WorkflowPurpose.APPROVAL">
            <summary>
            The approval
            </summary>
        </member>
        <member name="F:RnD.BackEnd.API.Model.Workflow.Enums.WorkflowPurpose.COLLECT_FEEDBACK">
            <summary>
            The collect feedback
            The collect feedback
            TheTheThe collect feedback
            </summary>
        </member>
        <member name="F:RnD.BackEnd.API.Model.Workflow.Enums.WorkflowPurpose.INTEGRATION">
            <summary>
            The integration
            </summary>
        </member>
        <member name="T:RnD.BackEnd.API.Model.Workflow.Enums.WorkflowStatus">
            <summary>
            Status possible for Workflow DTO enum
            </summary>
        </member>
        <member name="F:RnD.BackEnd.API.Model.Workflow.Enums.WorkflowStatus.CANCELLED">
            <summary>
            The cancelled
            </summary>
        </member>
        <member name="F:RnD.BackEnd.API.Model.Workflow.Enums.WorkflowStatus.IN_PROGRESS">
            <summary>
            The in progress
            </summary>
        </member>
        <member name="F:RnD.BackEnd.API.Model.Workflow.Enums.WorkflowStatus.APPROVED">
            <summary>
            The approved
            </summary>
        </member>
        <member name="F:RnD.BackEnd.API.Model.Workflow.Enums.WorkflowStatus.REJECTED">
            <summary>
            The rejected
            </summary>
        </member>
        <member name="T:RnD.BackEnd.API.Model.Workflow.Enums.WorkflowType">
            <summary>
            Types possible for Workflow DTO enum
            </summary>
        </member>
        <member name="F:RnD.BackEnd.API.Model.Workflow.Enums.WorkflowType.AUTOMATIC">
            <summary>
            Automatic workflow
            </summary>
        </member>
        <member name="F:RnD.BackEnd.API.Model.Workflow.Enums.WorkflowType.MANUAL">
            <summary>
            Manual workflow
            </summary>
        </member>
        <member name="T:RnD.BackEnd.API.Model.Workflow.GetDefinitionsDto">
            <summary>
            Get definitions DTO class
            </summary>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.GetDefinitionsDto.SourceType">
            <summary>
            Gets or sets the type of the source.
            </summary>
            <value>
            The type of the source.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.GetDefinitionsDto.WorkflowType">
            <summary>
            Gets or sets the type of the workflow.
            </summary>
            <value>
            The type of the workflow.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.GetDefinitionsDto.FetchInactive">
            <summary>
            Gets or sets a value indicating whether [fetch inactive].
            </summary>
            <value>
              <c>true</c> if [fetch inactive]; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="T:RnD.BackEnd.API.Model.Workflow.GetTasksDto">
            <summary>
            Get tasks DTO class
            </summary>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.GetTasksDto.SourceType">
            <summary>
            Gets or sets the type of the source.
            </summary>
            <value>
            The type of the source.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.GetTasksDto.UserId">
            <summary>
            Gets or sets the user identifier.
            </summary>
            <value>
            The user identifier.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.GetTasksDto.TaskStatus">
            <summary>
            Gets or sets the workflow task status.
            </summary>
            <value>
            The workflow task status.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.GetTasksDto.TaskCompleted">
            <summary>
            Represents an event that is raised when a task either successfully or unsuccessfully completes.
            </summary>
            <value>
              <c>true</c> if [task completed]; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.GetTasksDto.StepName">
            <summary>
            Gets or sets the name of the step.
            </summary>
            <value>
            The name of the step.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.GetTasksDto.WorkflowName">
            <summary>
            Gets or sets the name of the workflow.
            </summary>
            <value>
            The name of the workflow.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.GetTasksDto.WorkflowStatus">
            <summary>
            Gets or sets the workflow status.
            </summary>
            <value>
            The workflow status.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.GetTasksDto.SortField">
            <summary>
            Gets or sets the sort field.
            </summary>
            <value>
            The sort field.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.GetTasksDto.SortDirection">
            <summary>
            Gets or sets the sort direction.
            </summary>
            <value>
            The sort direction.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.GetTasksDto.ItemsPerPage">
            <summary>
            Gets or sets the items per page.
            </summary>
            <value>
            The items per page.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.GetTasksDto.CurrentPage">
            <summary>
            Gets or sets the current page.
            </summary>
            <value>
            The current page.
            </value>
        </member>
        <member name="T:RnD.BackEnd.API.Model.Workflow.GetUserInstancesDto">
            <summary>
            Get user instances DTO class
            </summary>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.GetUserInstancesDto.SourceType">
            <summary>
            Gets or sets the type of the source.
            </summary>
            <value>
            The type of the source.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.GetUserInstancesDto.WorkflowStatus">
            <summary>
            Gets or sets the workflow status.
            </summary>
            <value>
            The workflow status.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.GetUserInstancesDto.WorkflowType">
            <summary>
            Gets or sets the type of the workflow.
            </summary>
            <value>
            The type of the workflow.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.GetUserInstancesDto.WorkflowName">
            <summary>
            Gets or sets the name of the workflow.
            </summary>
            <value>
            The name of the workflow.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.GetUserInstancesDto.Purpose">
            <summary>
            Gets or sets the purpose.
            </summary>
            <value>
            The purpose.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.GetUserInstancesDto.SortField">
            <summary>
            Gets or sets the sort field.
            </summary>
            <value>
            The sort field.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.GetUserInstancesDto.SortDirection">
            <summary>
            Gets or sets the sort direction.
            </summary>
            <value>
            The sort direction.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.GetUserInstancesDto.ItemsPerPage">
            <summary>
            Gets or sets the items per page.
            </summary>
            <value>
            The items per page.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.GetUserInstancesDto.CurrentPage">
            <summary>
            Gets or sets the current page.
            </summary>
            <value>
            The current page.
            </value>
        </member>
        <member name="T:RnD.BackEnd.API.Model.Workflow.ReassignTaskDto">
            <summary>
            Reassign task DTO class
            </summary>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.ReassignTaskDto.Id">
            <summary>
            Gets or sets the task identifier.
            </summary>
            <value>
            The task identifier.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.ReassignTaskDto.NewUserId">
            <summary>
            Gets or sets the new userid for the task.
            </summary>
            <value>
            The new user identifier.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.ReassignTaskDto.Comments">
            <summary>
            Gets or sets the comments.
            </summary>
            <value>
            The comments.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.ReassignTaskDto.EmailRequest">
            <summary>
            Gets or sets the workflow email.
            </summary>
            <value>
            The workflow email.
            </value>
        </member>
        <member name="T:RnD.BackEnd.API.Model.Workflow.StartAutomaticWorkflowDto">
            <summary>
            Start automatic workflow request DTO class
            </summary>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.StartAutomaticWorkflowDto.WorkflowIds">
            <summary>
            Gets or sets the workflow ids.
            </summary>
            <value>
            The workflow ids.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.StartAutomaticWorkflowDto.SourceId">
            <summary>
            Gets or sets the source identifier.
            </summary>
            <value>
            The source identifier.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.StartAutomaticWorkflowDto.SourceUrl">
            <summary>
            Gets or sets the source URL.
            </summary>
            <value>
            The source URL.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.StartAutomaticWorkflowDto.SourceMetadata">
            <summary>
            Gets or sets the source metadata.
            </summary>
            <value>
            The source metadata.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.StartAutomaticWorkflowDto.EmailRequest">
            <summary>
            Gets or sets the workflow email.
            </summary>
            <value>
            The workflow email.
            </value>
        </member>
        <member name="T:RnD.BackEnd.API.Model.Workflow.StartWorkflowDto">
            <summary>
            Start workflow request DTO class
            </summary>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.StartWorkflowDto.DisplayName">
            <summary>
            Gets or sets the display name.
            </summary>
            <value>
            The display name.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.StartWorkflowDto.SourceId">
            <summary>
            Gets or sets the source identifier.
            </summary>
            <value>
            The source identifier.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.StartWorkflowDto.SourceUrl">
            <summary>
            Gets or sets the source URL.
            </summary>
            <value>
            The source URL.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.StartWorkflowDto.SourceType">
            <summary>
            Gets or sets the type of the source.
            </summary>
            <value>
            The type of the source.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.StartWorkflowDto.WorkflowType">
            <summary>
            Gets or sets the type of the workflow.
            </summary>
            <value>
            The type of the workflow.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.StartWorkflowDto.Purpose">
            <summary>
            Gets or sets the purpose.
            </summary>
            <value>
            The purpose.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.StartWorkflowDto.UserId">
            <summary>
            Gets or sets the user identifier.
            </summary>
            <value>
            The user identifier.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.StartWorkflowDto.AutoComplete">
            <summary>
            Gets or sets a value indicating whether [automatic complete].
            </summary>
            <value>
              <c>true</c> if [automatic complete]; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.StartWorkflowDto.WorkflowStatus">
            <summary>
            Gets or sets the workflow status.
            </summary>
            <value>
            The workflow status.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.StartWorkflowDto.Steps">
            <summary>
            Gets or sets the steps.
            </summary>
            <value>
            The steps.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.StartWorkflowDto.EmailRequest">
            <summary>
            Gets or sets the workflow email.
            </summary>
            <value>
            The workflow email.
            </value>
        </member>
        <member name="T:RnD.BackEnd.API.Model.Workflow.UpdateTaskDto">
            <summary>
            Update task DTO class
            </summary>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.UpdateTaskDto.Id">
            <summary>
            Gets or sets the identifier.
            </summary>
            <value>
            The identifier.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.UpdateTaskDto.TaskStatus">
            <summary>
            Gets or sets the task status.
            </summary>
            <value>
            The task status.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.UpdateTaskDto.TaskCompleted">
            <summary>
            Represents an event that is raised when a task either successfully or unsuccessfully completes.
            </summary>
            <value>
            The task completed.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.UpdateTaskDto.Comments">
            <summary>
            Gets or sets the comments.
            </summary>
            <value>
            The comments.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.UpdateTaskDto.EmailRequest">
            <summary>
            Gets or sets the workflow email.
            </summary>
            <value>
            The workflow email.
            </value>
        </member>
        <member name="T:RnD.BackEnd.API.Model.Workflow.UpdateTaskOutputDto">
            <summary>
            Update task output Dto
            </summary>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.UpdateTaskOutputDto.WorkflowStatus">
            <summary>
            Gets or sets the workflow status.
            </summary>
            <value>
            The workflow status.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.UpdateTaskOutputDto.WorkflowPurpose">
            <summary>
            Gets or sets the workflow purpose.
            </summary>
            <value>
            The workflow purpose.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.UpdateTaskOutputDto.SourceType">
            <summary>
            Gets or sets the type of the source.
            </summary>
            <value>
            The type of the source.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.UpdateTaskOutputDto.TaskId">
            <summary>
            Gets or sets the task identifier.
            </summary>
            <value>
            The task identifier.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.UpdateTaskOutputDto.WorkflowInstanceId">
            <summary>
            Gets or sets the workflow instance identifier.
            </summary>
            <value>
            The workflow instance identifier.
            </value>
        </member>
        <member name="T:RnD.BackEnd.API.Model.Workflow.WorkflowDefinitionDto">
            <summary>
            Workflow definition DTO class
            </summary>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowDefinitionDto.Id">
            <summary>
            Gets or sets the identifier.
            </summary>
            <value>
            The identifier.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowDefinitionDto.DisplayName">
            <summary>
            Gets or sets the display name.
            </summary>
            <value>
            The display name.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowDefinitionDto.RulesConditionType">
            <summary>
            Gets or sets the type of the rules condition.
            </summary>
            <value>
            The type of the rules condition.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowDefinitionDto.SourceType">
            <summary>
            Gets or sets the type of the source.
            </summary>
            <value>
            The type of the source.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowDefinitionDto.WorkflowType">
            <summary>
            Gets or sets the type of the workflow.
            </summary>
            <value>
            The type of the workflow.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowDefinitionDto.Purpose">
            <summary>
            Gets or sets the purpose.
            </summary>
            <value>
            The purpose.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowDefinitionDto.NumberOfSteps">
            <summary>
            Gets or sets the number of steps.
            </summary>
            <value>
            The number of steps.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowDefinitionDto.NumberOfRules">
            <summary>
            Gets or sets the number of rules.
            </summary>
            <value>
            The number of rules.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowDefinitionDto.IsActive">
            <summary>
            Gets or sets a value indicating whether this instance is active.
            </summary>
            <value>
              <c>true</c> if this instance is active; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="T:RnD.BackEnd.API.Model.Workflow.WorkflowEmailDto">
            <summary>
            Workflow Email DTO class
            </summary>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowEmailDto.TemplateId">
            <summary>
            Gets or sets the template identifier.
            </summary>
            <value>
            The template identifier.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowEmailDto.Recipients">
            <summary>
            Gets or sets the recipients.
            </summary>
            <value>
            The recipients.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowEmailDto.TemplateData">
            <summary>
            Gets or sets the template data.
            </summary>
            <value>
            The template data.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowEmailDto.Subject">
            <summary>
            Gets or sets the subject.
            </summary>
            <value>
            The subject.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowEmailDto.SendEmail">
            <summary>
            Gets or sets a value indicating whether [send email].
            </summary>
            <value>
              <c>true</c> if [send email]; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="T:RnD.BackEnd.API.Model.Workflow.WorkflowInstanceBySourceIdsDto">
            <summary>
            Workflow Instances by SourceId DTO class
            </summary>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowInstanceBySourceIdsDto.SourceIds">
            <summary>
            Gets or sets the source ids.
            </summary>
            <value>
            The source ids.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowInstanceBySourceIdsDto.Status">
            <summary>
            Gets or sets the status.
            </summary>
            <value>
            The status.
            </value>
        </member>
        <member name="T:RnD.BackEnd.API.Model.Workflow.WorkflowInstanceDto">
            <summary>
            Workflow instance DTO class
            </summary>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowInstanceDto.Id">
            <summary>
            Gets or sets the identifier.
            </summary>
            <value>
            The identifier.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowInstanceDto.WorkflowId">
            <summary>
            Gets or sets the workflow identifier.
            </summary>
            <value>
            The workflow identifier.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowInstanceDto.DisplayName">
            <summary>
            Gets or sets the display name.
            </summary>
            <value>
            The display name.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowInstanceDto.SourceId">
            <summary>
            Gets or sets the source identifier.
            </summary>
            <value>
            The source identifier.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowInstanceDto.SourceUrl">
            <summary>
            Gets or sets the source URL.
            </summary>
            <value>
            The source URL.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowInstanceDto.WorkflowStatus">
            <summary>
            Gets or sets the workflow status.
            </summary>
            <value>
            The workflow status.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowInstanceDto.SourceType">
            <summary>
            Gets or sets the type of the source.
            </summary>
            <value>
            The type of the source.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowInstanceDto.WorkflowType">
            <summary>
            Gets or sets the type of the workflow.
            </summary>
            <value>
            The type of the workflow.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowInstanceDto.Purpose">
            <summary>
            Gets or sets the purpose.
            </summary>
            <value>
            The purpose.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowInstanceDto.StateProcessingRequired">
            <summary>
            Gets or sets the state processing required.
            </summary>
            <value>
            The state processing required.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowInstanceDto.ActiveStepName">
            <summary>
            Gets or sets the name of the active step.
            </summary>
            <value>
            The name of the active step.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowInstanceDto.Steps">
            <summary>
            Gets or sets the steps.
            </summary>
            <value>
            The steps.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowInstanceDto.SysCreateUserId">
            <summary>
            Gets or sets the system create user identifier.
            </summary>
            <value>
            The system create user identifier.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowInstanceDto.SysModifyDate">
            <summary>
            Gets or sets the system modify date.
            </summary>
            <value>
            The system modify date.
            </value>
        </member>
        <member name="T:RnD.BackEnd.API.Model.Workflow.WorkflowInstancesListDto">
            <summary>
            Workflow instances list DTO class
            </summary>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowInstancesListDto.WorkflowInstances">
            <summary>
            Gets or sets the workflow instances.
            </summary>
            <value>
            The workflow instances.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowInstancesListDto.TotalRows">
            <summary>
            Gets or sets the total rows.
            </summary>
            <value>
            The total rows.
            </value>
        </member>
        <member name="T:RnD.BackEnd.API.Model.Workflow.WorkflowInstanceStepDto">
            <summary>
            Workflow instance step DTO class
            </summary>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowInstanceStepDto.Id">
            <summary>
            Gets or sets the identifier.
            </summary>
            <value>
            The identifier.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowInstanceStepDto.StepId">
            <summary>
            Gets or sets the step identifier.
            </summary>
            <value>
            The step identifier.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowInstanceStepDto.InstanceId">
            <summary>
            Gets or sets the instance identifier.
            </summary>
            <value>
            The instance identifier.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowInstanceStepDto.DisplayName">
            <summary>
            Gets or sets the display name.
            </summary>
            <value>
            The display name.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowInstanceStepDto.AllUsersMustApprove">
            <summary>
            Gets or sets a value indicating whether [all users must approve].
            </summary>
            <value>
              <c>true</c> if [all users must approve]; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowInstanceStepDto.SkipOverOtherTasks">
            <summary>
            Gets or sets a value indicating whether [skip over other tasks].
            </summary>
            <value>
              <c>true</c> if [skip over other tasks]; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowInstanceStepDto.Order">
            <summary>
            Gets or sets the order.
            </summary>
            <value>
            The order.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowInstanceStepDto.IsActive">
            <summary>
            Gets or sets a value indicating whether this instance is active.
            </summary>
            <value>
              <c>true</c> if this instance is active; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowInstanceStepDto.StepStatus">
            <summary>
            Gets or sets the step status.
            </summary>
            <value>
            The step status.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowInstanceStepDto.NumberOfActionsRequired">
            <summary>
            Gets or sets the number of actions required.
            </summary>
            <value>
            The number of actions required.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowInstanceStepDto.NumberOfActionsCompleted">
            <summary>
            Gets or sets the number of actions completed.
            </summary>
            <value>
            The number of actions completed.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowInstanceStepDto.Tasks">
            <summary>
            Gets or sets the tasks.
            </summary>
            <value>
            The tasks.
            </value>
        </member>
        <member name="T:RnD.BackEnd.API.Model.Workflow.WorkflowInstanceTaskDto">
            <summary>
            Workflow instance task DTO class
            </summary>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowInstanceTaskDto.Id">
            <summary>
            Gets or sets the identifier.
            </summary>
            <value>
            The identifier.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowInstanceTaskDto.UserId">
            <summary>
            Gets or sets the user identifier.
            </summary>
            <value>
            The user identifier.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowInstanceTaskDto.TaskStatus">
            <summary>
            Gets or sets the task status.
            </summary>
            <value>
            The task status.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowInstanceTaskDto.TaskCompleted">
            <summary>
            Represents an event that is raised when a task either successfully or unsuccessfully completes.
            </summary>
            <value>
              <c>true</c> if [task completed]; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowInstanceTaskDto.TaskActivated">
            <summary>
            Gets or sets a value indicating whether [task activated].
            </summary>
            <value>
              <c>true</c> if [task activated]; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowInstanceTaskDto.Comments">
            <summary>
            Gets or sets the comments.
            </summary>
            <value>
            The comments.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowInstanceTaskDto.SysModifyDate">
            <summary>
            Gets or sets the system modify date.
            </summary>
            <value>
            The system modify date.
            </value>
        </member>
        <member name="T:RnD.BackEnd.API.Model.Workflow.WorkflowTaskDto">
            <summary>
            Workflow task DTO class
            </summary>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowTaskDto.Id">
            <summary>
            Gets or sets the identifier.
            </summary>
            <value>
            The identifier.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowTaskDto.WorkflowInstanceId">
            <summary>
            Gets or sets the workflow instance identifier.
            </summary>
            <value>
            The workflow instance identifier.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowTaskDto.StepId">
            <summary>
            Gets or sets the step identifier.
            </summary>
            <value>
            The step identifier.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowTaskDto.UserId">
            <summary>
            Gets or sets the user identifier.
            </summary>
            <value>
            The user identifier.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowTaskDto.TaskStatus">
            <summary>
            Gets or sets the task status.
            </summary>
            <value>
            The task status.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowTaskDto.TaskCompleted">
            <summary>
            Represents an event that is raised when a task either successfully or unsuccessfully completes.
            </summary>
            <value>
              <c>true</c> if [task completed]; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowTaskDto.TaskActivated">
            <summary>
            Gets or sets a value indicating whether [task activated].
            </summary>
            <value>
              <c>true</c> if [task activated]; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowTaskDto.Comments">
            <summary>
            Gets or sets the comments.
            </summary>
            <value>
            The comments.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowTaskDto.SourceId">
            <summary>
            Gets or sets the source identifier.
            </summary>
            <value>
            The source identifier.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowTaskDto.SourceUrl">
            <summary>
            Gets or sets the source URL.
            </summary>
            <value>
            The source URL.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowTaskDto.WorkflowStatus">
            <summary>
            Gets or sets the workflow status.
            </summary>
            <value>
            The workflow status.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowTaskDto.WorkflowName">
            <summary>
            Gets or sets the name of the workflow.
            </summary>
            <value>
            The name of the workflow.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowTaskDto.StepName">
            <summary>
            Gets or sets the name of the step.
            </summary>
            <value>
            The name of the step.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowTaskDto.StepOrder">
            <summary>
            Gets or sets the step order.
            </summary>
            <value>
            The step order.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowTaskDto.SysModifyDate">
            <summary>
            Gets or sets the system modify date.
            </summary>
            <value>
            The system modify date.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowTaskDto.TotalRows">
            <summary>
            Gets or sets the total rows.
            </summary>
            <value>
            The total rows.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowTaskDto.UserAllowedToEdit">
            <summary>
            Gets or sets a value indicating whether [user allowed to edit].
            </summary>
            <value>
              <c>true</c> if [user allowed to edit]; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="T:RnD.BackEnd.API.Model.Workflow.WorkflowTasksListDto">
            <summary>
            Workflow tasks list DTO class
            </summary>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowTasksListDto.Tasks">
            <summary>
            Gets or sets the tasks.
            </summary>
            <value>
            The tasks.
            </value>
        </member>
        <member name="P:RnD.BackEnd.API.Model.Workflow.WorkflowTasksListDto.TotalRows">
            <summary>
            Gets or sets the total rows.
            </summary>
            <value>
            The total rows.
            </value>
        </member>
        <member name="M:RnD.BackEnd.API.Services.ConsumoPDFService.GetCrayonPDFsRawText">
            <summary>
            Obtém o texto bruto de todos os PDFs Crayon no container Azure
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.Services.ConsumoPDFService.GetCrayonPDFsExtractedData">
            <summary>
            Obtém os dados extraídos de todos os PDFs Crayon no container Azure
            </summary>
        </member>
        <member name="T:RnD.BackEnd.API.Services.CrayonTestExtractorService">
            <summary>
            Serviço para extração de dados de PDFs Crayon para testes (sem persistência na BD)
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.Services.CrayonTestExtractorService.GetCrayonPDFsRawText">
            <summary>
            Obtém o texto bruto de todos os PDFs Crayon no container
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.Services.CrayonTestExtractorService.GetSpecificPDFExtractedData(System.String)">
            <summary>
            Obtém os dados extraídos de um PDF específico para debug
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.Services.CrayonTestExtractorService.GetCrayonPDFsExtractedData">
            <summary>
            Obtém os dados extraídos de todos os PDFs Crayon no container
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.Services.CrayonTestExtractorService.ExtractTextWithDocNet(System.IO.MemoryStream)">
            <summary>
            Extrai texto de PDF usando DocNet
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.Services.CrayonTestExtractorService.IsCrayonPDF(System.String)">
            <summary>
            Verifica se o PDF é do tipo Crayon
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.Services.CrayonTestExtractorService.ExtractCrayonData(System.String,System.IO.MemoryStream)">
            <summary>
            Extrai dados completos de um PDF Crayon
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.Services.CrayonTestExtractorService.ExtractDataFromText(System.String)">
            <summary>
            Extrai dados específicos do texto do PDF
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.Services.CrayonTestExtractorService.ExtractCliente(System.String)">
            <summary>
            Extrai o nome do cliente (procura por "BI4ALL Consultores de Gestão, Lda")
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.Services.CrayonTestExtractorService.ExtractPeriodo(System.String)">
            <summary>
            Extrai período de serviço ou usa data da fatura para criar período mensal - versão robusta
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.Services.CrayonTestExtractorService.ExtractPeriodoFromInvoiceDate(System.String)">
            <summary>
            Extrai período baseado na data da fatura (fallback quando não há período explícito)
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.Services.CrayonTestExtractorService.ExtractPeriodoEspecificoProduto(System.String[],System.Int32,System.DateTime,System.DateTime)">
            <summary>
            Extrai período específico para um produto individual - VERSÃO MELHORADA
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.Services.CrayonTestExtractorService.ExtractPeriodoComMapeamento(System.String[],System.Int32,System.DateTime,System.DateTime)">
            <summary>
            NOVA ESTRATÉGIA: Extrai período usando mapeamento robusto
            Funciona para TODOS os formatos de PDF (2023, 2024, 2025, especiais)
            </summary>
        </member>
        <member name="T:RnD.BackEnd.API.Services.CrayonTestExtractorService.PeriodoEncontrado">
            <summary>
            Classe para mapear períodos encontrados no texto
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.Services.CrayonTestExtractorService.ExtrairTodosPeriodosDoTexto(System.String[])">
            <summary>
            Extrai TODOS os períodos específicos encontrados no texto
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.Services.CrayonTestExtractorService.DeterminarTipoOrigem(System.String)">
            <summary>
            Determina o tipo de origem do período baseado no contexto
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.Services.CrayonTestExtractorService.EncontrarPeriodoMaisProximo(System.Collections.Generic.List{RnD.BackEnd.API.Services.CrayonTestExtractorService.PeriodoEncontrado},System.Int32,System.String[])">
            <summary>
            Encontra o período mais próximo da linha do produto - VERSÃO INTELIGENTE
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.Services.CrayonTestExtractorService.EhLinhaDoAzurePlan(System.Int32,System.String[])">
            <summary>
            Verifica se a linha pertence especificamente ao Azure Plan - VERSÃO ULTRA ESPECÍFICA
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.Services.CrayonTestExtractorService.TryParseDate(System.String,System.DateTime@)">
            <summary>
            Tenta fazer parse de uma data em vários formatos
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.Services.CrayonTestExtractorService.NormalizeDecimalValue(System.String)">
            <summary>
            Normaliza valores decimais, considerando formatos europeus e americanos
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.Services.CrayonTestExtractorService.ExtractProdutosComPeriodos(System.String)">
            <summary>
            Extrai todos os produtos com seus períodos específicos
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.Services.CrayonTestExtractorService.TryExtractProdutoFromLine(System.String,System.String@,System.Decimal@,System.Decimal@)">
            <summary>
            Tenta extrair informações de produto de uma linha
            Suporta formatos:
            2023: "1,00 Pce 243,66 23.00 56,04 243,66"
            2024/2025: "1.00 PCE 5,189.09 23.00 1,193.49 5,189.09"
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.Services.CrayonTestExtractorService.CleanDescription(System.String)">
            <summary>
            Limpa a descrição removendo texto desnecessário
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.Services.CrayonTestExtractorService.DebugPeriodsAsync(System.String)">
            <summary>
            Debug de períodos extraídos de um PDF específico - SIMPLIFICADO
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.Services.IPDFProcessor.PodeProcessar(System.String)">
            <summary>
            Determina se este processador pode processar o tipo de PDF fornecido
            </summary>
            <param name="textoCompleto">Texto extraído do PDF</param>
            <returns>True se pode processar, False caso contrário</returns>
        </member>
        <member name="M:RnD.BackEnd.API.Services.IPDFProcessor.ProcessarPDF(System.String,System.Int32)">
            <summary>
            Processa o PDF e extrai os dados de consumo
            </summary>
            <param name="textoCompleto">Texto extraído do PDF</param>
            <param name="fileId">ID do arquivo para logging</param>
            <returns>Lista de consumos extraídos</returns>
        </member>
        <member name="P:RnD.BackEnd.API.Services.IPDFProcessor.TipoProcessador">
            <summary>
            Retorna o tipo/identificador do processador
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.Services.UserService.GetUserByAzureIdAsync(System.String)">
            <summary>
            Verifica se um user existe no banco de dados pelo AzureId
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.Services.UserService.CreateUserAsync(System.String,System.String,System.String)">
            <summary>
            Cria um novo user no banco de dados
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.Services.UserService.AssignDefaultPermissionsAsync(System.Object)">
            <summary>
            Atribui permissões básicas a um novo user
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.Services.UserService.GetUserPermissionsAsync(System.Int32)">
            <summary>
            Obtém uma lista de todas as permissões que um user possui
            </summary>
        </member>
        <member name="T:RnD.BackEnd.API.SwashbuckleExamples.DataEntryController.GetEntities200">
            <summary>
            Get Entities response example for 200
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.SwashbuckleExamples.DataEntryController.GetEntities200.GetExamples">
            <summary>
            Gets the examples.
            </summary>
            <returns>
            The example of a 200 for Get Entities response.
            </returns>
        </member>
        <member name="T:RnD.BackEnd.API.SwashbuckleExamples.DynamicEntityController.AddDynamicEntity">
            <summary>
            Add dynamic Entity request example
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.SwashbuckleExamples.DynamicEntityController.AddDynamicEntity.GetExamples">
            <summary>
            Gets the examples.
            </summary>
            <returns>
            The examples of requests to Add Dynamic Entity.
            </returns>
        </member>
        <member name="T:RnD.BackEnd.API.SwashbuckleExamples.DynamicEntityController.AddDynamicEntity200">
            <summary>
            Add Dynamic Entity response example for 200
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.SwashbuckleExamples.DynamicEntityController.AddDynamicEntity200.GetExamples">
            <summary>
            Gets the examples.
            </summary>
            <returns>
            The example of a Add~Dynamic Entity 200 response.
            </returns>
        </member>
        <member name="T:RnD.BackEnd.API.SwashbuckleExamples.DynamicEntityController.GetDynamicEntity200">
            <summary>
            Get Dynamic Entity response example for 200
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.SwashbuckleExamples.DynamicEntityController.GetDynamicEntity200.GetExamples">
            <summary>
            Gets the examples.
            </summary>
            <returns>
            The example of a Get Dynamic Entity 200 response.
            </returns>
        </member>
        <member name="T:RnD.BackEnd.API.SwashbuckleExamples.DynamicEntityController.ListDynamicEntity200">
            <summary>
            The example of a 200 response when listing Dynamic Entities.
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.SwashbuckleExamples.DynamicEntityController.ListDynamicEntity200.GetExamples">
            <summary>
            Gets the examples.
            </summary>
            <returns>
            The example of a 200 for a list response.
            </returns>
        </member>
        <member name="T:RnD.BackEnd.API.SwashbuckleExamples.DynamicEntityController.UpdateDynamicEntity">
            <summary>
            Update dynamic Entity request example
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.SwashbuckleExamples.DynamicEntityController.UpdateDynamicEntity.GetExamples">
            <summary>
            Gets the examples.
            </summary>
            <returns>
            The example of a request to Update Dynamic Entity.
            </returns>
        </member>
        <member name="T:RnD.BackEnd.API.SwashbuckleExamples.DynamicEntityController.UpdateDynamicEntity200">
            <summary>
            Update Dynamic Entity response example for 200
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.SwashbuckleExamples.DynamicEntityController.UpdateDynamicEntity200.GetExamples">
            <summary>
            Gets the examples.
            </summary>
            <returns>
            The example of a Update Dynamic Entity 200 response.
            </returns>
        </member>
        <member name="T:RnD.BackEnd.API.SwashbuckleExamples.DynamicEntityController.UpdateDynamicEntity412">
            <summary>
            The example for a 412 response.
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.SwashbuckleExamples.DynamicEntityController.UpdateDynamicEntity412.GetExamples">
            <summary>
            Gets the examples.
            </summary>
            <returns>
            The example for a Update Dynamic Entity 412 response.
            </returns>
        </member>
        <member name="T:RnD.BackEnd.API.SwashbuckleExamples.EmailController.SendCodeAcademyEmail">
            <summary>
            Send Code Academy Email request example
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.SwashbuckleExamples.EmailController.SendCodeAcademyEmail.GetExamples">
            <summary>
            Gets the examples.
            </summary>
            <returns>
            The example of a request to Send Code Academy Email.
            </returns>
        </member>
        <member name="T:RnD.BackEnd.API.SwashbuckleExamples.EmailController.SendDefaultTemplateEmail">
            <summary>
            Send Default Template Email request example
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.SwashbuckleExamples.EmailController.SendDefaultTemplateEmail.GetExamples">
            <summary>
            Gets the examples.
            </summary>
            <returns>
            The example of a request to Send Default Template Email.
            </returns>
        </member>
        <member name="T:RnD.BackEnd.API.SwashbuckleExamples.EmailController.SendDefaultTemplateEmailWithAttachments">
            <summary>
            Send Template Email With Attachments request example
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.SwashbuckleExamples.EmailController.SendDefaultTemplateEmailWithAttachments.GetExamples">
            <summary>
            Gets the examples.
            </summary>
            <returns>
            The example of a request to Send Default Template Email With Attachments.
            </returns>
        </member>
        <member name="T:RnD.BackEnd.API.SwashbuckleExamples.EmailController.SendTemplatedEmail">
            <summary>
            Send Templated Email request example
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.SwashbuckleExamples.EmailController.SendTemplatedEmail.GetExamples">
            <summary>
            Gets the examples.
            </summary>
            <returns>
            The example of a request to Send Templated Email.
            </returns>
        </member>
        <member name="T:RnD.BackEnd.API.SwashbuckleExamples.ResponseBoolExample200">
            <summary>
            Bool response example 200
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.SwashbuckleExamples.ResponseBoolExample200.GetExamples">
            <summary>
            Gets the examples.
            </summary>
            <returns>
            The example of a 200 for a bool response.
            </returns>
        </member>
        <member name="T:RnD.BackEnd.API.SwashbuckleExamples.ResponseExample400">
            <summary>
            The example for a 400 response.
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.SwashbuckleExamples.ResponseExample400.GetExamples">
            <summary>
            Gets the examples.
            </summary>
            <returns>
            The example for a 400 response.
            </returns>
        </member>
        <member name="T:RnD.BackEnd.API.SwashbuckleExamples.ResponseExample404">
            <summary>
            The example for a 404 response.
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.SwashbuckleExamples.ResponseExample404.GetExamples">
            <summary>
            Gets the examples.
            </summary>
            <returns>
            The example for a 404 response.
            </returns>
        </member>
        <member name="T:RnD.BackEnd.API.SwashbuckleExamples.ResponseExample409">
            <summary>
            The example for a 409 response.
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.SwashbuckleExamples.ResponseExample409.GetExamples">
            <summary>
            Gets the examples.
            </summary>
            <returns>
            The example for a 409 response.
            </returns>
        </member>
        <member name="T:RnD.BackEnd.API.SwashbuckleExamples.ResponseExample500">
            <summary>
            Response example of a 500
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.SwashbuckleExamples.ResponseExample500.GetExamples">
            <summary>
            Gets the examples.
            </summary>
            <returns>
            The Service Output.
            </returns>
        </member>
        <member name="T:RnD.BackEnd.API.SwashbuckleExamples.VehicleController.Vehicle200">
            <summary>
            Vehicle response example for 200
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.SwashbuckleExamples.VehicleController.Vehicle200.GetExamples">
            <summary>
            Gets the examples.
            </summary>
            <returns>
            The example of a 200 response.
            </returns>
        </member>
        <member name="T:RnD.BackEnd.API.SwashbuckleExamples.VehicleController.Vehicle201">
            <summary>
            Vehicle example of a 201 response
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.SwashbuckleExamples.VehicleController.Vehicle201.GetExamples">
            <summary>
            Gets the examples.
            </summary>
            <returns>
            The example of a vehicle response for 201.
            </returns>
        </member>
        <member name="T:RnD.BackEnd.API.SwashbuckleExamples.VehicleController.VehicleAddOrUpdate">
            <summary>
            Vehicle add or update request example
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.SwashbuckleExamples.VehicleController.VehicleAddOrUpdate.GetExamples">
            <summary>
            Gets the examples.
            </summary>
            <returns>
            The example of a request to add or update.
            </returns>
        </member>
        <member name="T:RnD.BackEnd.API.SwashbuckleExamples.VehicleController.VehicleCreate">
            <summary>
            Vehicle create request example
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.SwashbuckleExamples.VehicleController.VehicleCreate.GetExamples">
            <summary>
            Gets the examples.
            </summary>
            <returns>The create request example</returns>
        </member>
        <member name="T:RnD.BackEnd.API.SwashbuckleExamples.VehicleController.VehicleList200">
            <summary>
            The example of a 200 response when listing vehicles.
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.SwashbuckleExamples.VehicleController.VehicleList200.GetExamples">
            <summary>
            Gets the examples.
            </summary>
            <returns>
            The example of a 200 for a list response.
            </returns>
        </member>
        <member name="T:RnD.BackEnd.API.SwashbuckleExamples.VehicleController.VehicleList201">
            <summary>
            The example of a response 201 for a list of created vehicles.
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.SwashbuckleExamples.VehicleController.VehicleList201.GetExamples">
            <summary>
            Gets the examples.
            </summary>
            <returns>
            The example of a response 201 for a list of created vehicles
            </returns>
        </member>
        <member name="T:RnD.BackEnd.API.SwashbuckleExamples.VehicleController.VehicleMultipleCreate">
            <summary>
            Multiple vehicle create request example
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.SwashbuckleExamples.VehicleController.VehicleMultipleCreate.GetExamples">
            <summary>
            Gets the examples.
            </summary>
            <returns>
            The example of a request to create multiple vehicles.
            </returns>
        </member>
        <member name="T:RnD.BackEnd.API.SwashbuckleExamples.VehicleController.VehiclePatch">
            <summary>
            Vehicle patch request example
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.SwashbuckleExamples.VehicleController.VehiclePatch.GetExamples">
            <summary>
            Gets the examples.
            </summary>
            <returns>
            The example of a request to patch a vehicle
            </returns>
        </member>
        <member name="T:RnD.BackEnd.API.SwashbuckleExamples.WeatherForecastController.CreateWeather">
            <summary>
            Create Weather request example
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.SwashbuckleExamples.WeatherForecastController.CreateWeather.GetExamples">
            <summary>
            Gets the examples.
            </summary>
            <returns>
            The example of a request to Create Weather.
            </returns>
        </member>
        <member name="T:RnD.BackEnd.API.SwashbuckleExamples.WeatherForecastController.ListWeather">
            <summary>
            Multiple Weather forecast create request example
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.SwashbuckleExamples.WeatherForecastController.ListWeather.GetExamples">
            <summary>
            Gets the examples.
            </summary>
            <returns>
            The example of a request to create multiple Weather forecast.
            </returns>
        </member>
        <member name="T:RnD.BackEnd.API.SwashbuckleExamples.WeatherForecastController.PatchWeather">
            <summary>
            Weather patch request example
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.SwashbuckleExamples.WeatherForecastController.PatchWeather.GetExamples">
            <summary>
            Gets the examples.
            </summary>
            <returns>
            The example of a request to patch a weather
            </returns>
        </member>
        <member name="T:RnD.BackEnd.API.SwashbuckleExamples.WeatherForecastController.UpdateWeather">
            <summary>
            Weather update request example
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.SwashbuckleExamples.WeatherForecastController.UpdateWeather.GetExamples">
            <summary>
            Gets the examples.
            </summary>
            <returns>
            The example of a request to update.
            </returns>
        </member>
        <member name="T:RnD.BackEnd.API.SwashbuckleExamples.WeatherForecastController.Weather200">
            <summary>
            Weather Forecast response example for 200
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.SwashbuckleExamples.WeatherForecastController.Weather200.GetExamples">
            <summary>
            Gets the examples.
            </summary>
            <returns>
            The example of a 200 response.
            </returns>
        </member>
        <member name="T:RnD.BackEnd.API.SwashbuckleExamples.WeatherForecastController.Weather201">
            <summary>
            Weather example of a 201 response
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.SwashbuckleExamples.WeatherForecastController.Weather201.GetExamples">
            <summary>
            Gets the examples.
            </summary>
            <returns>
            The example of a weather response for 201.
            </returns>
        </member>
        <member name="T:RnD.BackEnd.API.SwashbuckleExamples.WeatherForecastController.WeatherList200">
            <summary>
            The example of a 200 response when listing vehicles.
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.SwashbuckleExamples.WeatherForecastController.WeatherList200.GetExamples">
            <summary>
            Gets the examples.
            </summary>
            <returns>
            The example of a 200 for a list response.
            </returns>
        </member>
        <member name="T:RnD.BackEnd.API.SwashbuckleExamples.WeatherForecastController.WeatherList201">
            <summary>
            The example of a response 201 for a list of created Weathers.
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.SwashbuckleExamples.WeatherForecastController.WeatherList201.GetExamples">
            <summary>
            Gets the examples.
            </summary>
            <returns>
            The example of a response 201 for a list of created Weathers
            </returns>
        </member>
        <member name="T:RnD.BackEnd.API.SwashbuckleExamples.WorkflowController.GetDefinitions200">
            <summary>
            The example of a 200 response when listing definitions.
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.SwashbuckleExamples.WorkflowController.GetDefinitions200.GetExamples">
            <summary>
            Gets the examples.
            </summary>
            <returns>
            The example of a 200 for a list response.
            </returns>
        </member>
        <member name="T:RnD.BackEnd.API.SwashbuckleExamples.WorkflowController.GetInstances200">
            <summary>
            Get Instances response example for 200
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.SwashbuckleExamples.WorkflowController.GetInstances200.GetExamples">
            <summary>
            Gets the examples.
            </summary>
            <returns>
            The example of a 200 for a list response.
            </returns>
        </member>
        <member name="T:RnD.BackEnd.API.SwashbuckleExamples.WorkflowController.GetInstancesBySourceIdsAndStatus">
            <summary>
            Instances by SourceIds request example
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.SwashbuckleExamples.WorkflowController.GetInstancesBySourceIdsAndStatus.GetExamples">
            <summary>
            Gets the examples.
            </summary>
            <returns>
            Examples of requests to Get Instances by SourceIds.
            </returns>
        </member>
        <member name="T:RnD.BackEnd.API.SwashbuckleExamples.WorkflowController.GetTaskById200">
            <summary>
            Get Task by id response example for 200
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.SwashbuckleExamples.WorkflowController.GetTaskById200.GetExamples">
            <summary>
            Gets the examples.
            </summary>
            <returns>
            The example of a 200 for a task by id response.
            </returns>
        </member>
        <member name="T:RnD.BackEnd.API.SwashbuckleExamples.WorkflowController.GetTasks200">
            <summary>
            Get Tasks response example for 200
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.SwashbuckleExamples.WorkflowController.GetTasks200.GetExamples">
            <summary>
            Gets the examples.
            </summary>
            <returns>
            The example of a 200 for tasks response.
            </returns>
        </member>
        <member name="T:RnD.BackEnd.API.SwashbuckleExamples.WorkflowController.GetUserInstances200">
            <summary>
            Get User Instances response example for 200
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.SwashbuckleExamples.WorkflowController.GetUserInstances200.GetExamples">
            <summary>
            Gets the examples.
            </summary>
            <returns>
            The example of a 200 for a list response.
            </returns>
        </member>
        <member name="T:RnD.BackEnd.API.SwashbuckleExamples.WorkflowController.ReassignRequest">
            <summary>
            Reassign Task request example
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.SwashbuckleExamples.WorkflowController.ReassignRequest.GetExamples">
            <summary>
            Gets the examples.
            </summary>
            <returns>
            The example of a request to Reassign a task.
            </returns>
        </member>
        <member name="T:RnD.BackEnd.API.SwashbuckleExamples.WorkflowController.StartAutomaticWorkflowRequest">
            <summary>
            Start Automatic Workflow request example
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.SwashbuckleExamples.WorkflowController.StartAutomaticWorkflowRequest.GetExamples">
            <summary>
            Gets the examples.
            </summary>
            <returns>
            The example of a request to Start Automatic Workflow.
            </returns>
        </member>
        <member name="T:RnD.BackEnd.API.SwashbuckleExamples.WorkflowController.StartWorkflow200">
            <summary>
            Start Workflow response example for 200
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.SwashbuckleExamples.WorkflowController.StartWorkflow200.GetExamples">
            <summary>
            Gets the examples.
            </summary>
            <returns>
            The example of a Start Workflow 200 response.
            </returns>
        </member>
        <member name="T:RnD.BackEnd.API.SwashbuckleExamples.WorkflowController.StartWorkflowRequest">
            <summary>
            Workflow Start request example
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.SwashbuckleExamples.WorkflowController.StartWorkflowRequest.GetExamples">
            <summary>
            Gets the examples.
            </summary>
            <returns>
            The example of a request to Start Workflow.
            </returns>
        </member>
        <member name="T:RnD.BackEnd.API.SwashbuckleExamples.WorkflowController.UpdateTaskRequest">
            <summary>
            Update Task request example
            </summary>
            <seealso cref="!:Swashbuckle.AspNetCore.Filters.IMultipleExamplesProvider&lt;RnD.BackEnd.API.Model.Workflow.UpdateTaskDto&gt;" />
        </member>
        <member name="M:RnD.BackEnd.API.SwashbuckleExamples.WorkflowController.UpdateTaskRequest.GetExamples">
            <summary>
            Gets the examples.
            </summary>
            <returns>
            The example of a request to update task.
            </returns>
        </member>
        <member name="T:RnD.BackEnd.API.SwashbuckleExamples.WorkflowController.UpdateTasks200">
            <summary>
            Update tasks response example for 200
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.SwashbuckleExamples.WorkflowController.UpdateTasks200.GetExamples">
            <summary>
            Gets the examples.
            </summary>
            <returns>
            The example of a 200 for a list response.
            </returns>
        </member>
        <member name="T:RnD.BackEnd.API.SwashbuckleExamples.WorkflowController.UpdateTasksRequest">
            <summary>
            Update Multiple Tasks request example
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.SwashbuckleExamples.WorkflowController.UpdateTasksRequest.GetExamples">
            <summary>
            Gets the examples.
            </summary>
            <returns>
            The example of a request to update multiple task.
            </returns>
        </member>
        <member name="T:RnD.BackEnd.API.SwashbuckleExamples.WorkflowController.WorkflowEmailRequest">
            <summary>
            Send Email request example
            </summary>
        </member>
        <member name="M:RnD.BackEnd.API.SwashbuckleExamples.WorkflowController.WorkflowEmailRequest.GetExamples">
            <summary>
            Gets the examples.
            </summary>
            <returns>
            The example of a request to Send Email.
            </returns>
        </member>
    </members>
</doc>
