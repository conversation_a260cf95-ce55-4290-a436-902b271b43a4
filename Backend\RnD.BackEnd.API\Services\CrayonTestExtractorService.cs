using Azure.Storage.Blobs;
using Docnet.Core;
using Docnet.Core.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using RnD.BackEnd.API.Models;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace RnD.BackEnd.API.Services
{
    /// <summary>
    /// Serviço para extração de dados de PDFs Crayon para testes (sem persistência na BD)
    /// </summary>
    public class CrayonTestExtractorService
    {
        private readonly BlobServiceClient _blobServiceClient;
        private readonly ILogger<CrayonTestExtractorService> _logger;
        private readonly string _containerName;

        public CrayonTestExtractorService(
            BlobServiceClient blobServiceClient,
            ILogger<CrayonTestExtractorService> logger,
            IConfiguration configuration)
        {
            _blobServiceClient = blobServiceClient;
            _logger = logger;
            _containerName = configuration["AzureStoragePDF:ContainerName"] ?? "consumo-pdf";
        }

        /// <summary>
        /// Obtém o texto bruto de todos os PDFs Crayon no container
        /// </summary>
        public async Task<Dictionary<string, string>> GetCrayonPDFsRawText()
        {
            var result = new Dictionary<string, string>();

            try
            {
                var containerClient = _blobServiceClient.GetBlobContainerClient(_containerName);

                await foreach (var blobItem in containerClient.GetBlobsAsync())
                {
                    if (!blobItem.Name.EndsWith(".pdf", StringComparison.OrdinalIgnoreCase))
                        continue;

                    try
                    {
                        var blobClient = containerClient.GetBlobClient(blobItem.Name);

                        using var memoryStream = new MemoryStream();
                        await blobClient.DownloadToAsync(memoryStream);
                        memoryStream.Position = 0;

                        var textoBruto = ExtractTextWithDocNet(memoryStream);

                        if (IsCrayonPDF(textoBruto))
                        {
                            result[blobItem.Name] = textoBruto;
                            _logger.LogInformation($"PDF Crayon identificado: {blobItem.Name}");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"Erro ao processar arquivo {blobItem.Name}");
                        result[blobItem.Name] = $"ERRO: {ex.Message}";
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao listar PDFs do container");
                throw;
            }

            return result;
        }

        /// <summary>
        /// Obtém os dados extraídos de um PDF específico para debug
        /// </summary>
        public async Task<CrayonPDFExtractionResult> GetSpecificPDFExtractedData(string fileName)
        {
            try
            {
                Console.WriteLine($"🚨🚨🚨 [SERVICE] INICIANDO EXTRAÇÃO DO PDF: {fileName}");
                _logger.LogInformation($"🚨🚨🚨 [SERVICE] INICIANDO EXTRAÇÃO DO PDF: {fileName}");

                var containerClient = _blobServiceClient.GetBlobContainerClient(_containerName);
                var blobClient = containerClient.GetBlobClient(fileName);

                if (!await blobClient.ExistsAsync())
                {
                    throw new FileNotFoundException($"PDF {fileName} não encontrado no container");
                }

                // Baixar e extrair texto do PDF
                var downloadResult = await blobClient.DownloadContentAsync();
                var pdfBytes = downloadResult.Value.Content.ToArray();

                Console.WriteLine($"🚨🚨🚨 [SERVICE] PDF BAIXADO, TAMANHO: {pdfBytes.Length} bytes");
                _logger.LogInformation($"🚨🚨🚨 [SERVICE] PDF BAIXADO, TAMANHO: {pdfBytes.Length} bytes");

                var textoBruto = ExtractTextWithDocNet(new MemoryStream(pdfBytes));

                Console.WriteLine($"🚨🚨🚨 [SERVICE] TEXTO EXTRAÍDO, TAMANHO: {textoBruto.Length} chars");
                _logger.LogInformation($"🚨🚨🚨 [SERVICE] TEXTO EXTRAÍDO, TAMANHO: {textoBruto.Length} chars");

                // Verificar se é PDF Crayon
                bool isCrayonPDF = IsCrayonPDF(textoBruto);

                Console.WriteLine($"🚨🚨🚨 [SERVICE] É CRAYON PDF: {isCrayonPDF}");
                _logger.LogInformation($"🚨🚨🚨 [SERVICE] É CRAYON PDF: {isCrayonPDF}");

                if (!isCrayonPDF)
                {
                    return new CrayonPDFExtractionResult
                    {
                        NomeArquivo = fileName,
                        IsCrayonPDF = false,
                        TextoBruto = textoBruto,
                        DadosExtraidos = new List<CrayonExtractedData>(),
                        TemErro = true,
                        MensagemErro = "Não é um PDF Crayon"
                    };
                }

                // Extrair dados
                var dadosExtraidos = ExtractDataFromText(textoBruto);

                Console.WriteLine($"🚨🚨🚨 [SERVICE] DADOS EXTRAÍDOS: {dadosExtraidos.Count} produtos");
                _logger.LogInformation($"🚨🚨🚨 [SERVICE] DADOS EXTRAÍDOS: {dadosExtraidos.Count} produtos");

                return new CrayonPDFExtractionResult
                {
                    NomeArquivo = fileName,
                    IsCrayonPDF = true,
                    TextoBruto = textoBruto,
                    DadosExtraidos = dadosExtraidos,
                    TemErro = false,
                    MensagemErro = null
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"🚨🚨🚨 [SERVICE] ERRO: {ex.Message}");
                _logger.LogError(ex, $"Erro ao extrair dados do PDF {fileName}");

                return new CrayonPDFExtractionResult
                {
                    NomeArquivo = fileName,
                    IsCrayonPDF = false,
                    TextoBruto = "",
                    DadosExtraidos = new List<CrayonExtractedData>(),
                    TemErro = true,
                    MensagemErro = ex.Message
                };
            }
        }

        /// <summary>
        /// Testa extração de TODOS os PDFs disponíveis no container
        /// </summary>
        public async Task<List<object>> TestarTodosPDFsAsync()
        {
            var resultados = new List<object>();
            var containerClient = _blobServiceClient.GetBlobContainerClient(_containerName);

            Console.WriteLine($"🚨🚨🚨 [SERVICE] INICIANDO TESTE DE TODOS OS PDFs");
            _logger.LogInformation($"🚨🚨🚨 [SERVICE] INICIANDO TESTE DE TODOS OS PDFs");

            await foreach (var blobItem in containerClient.GetBlobsAsync())
            {
                if (!blobItem.Name.EndsWith(".pdf", StringComparison.OrdinalIgnoreCase))
                    continue;

                try
                {
                    Console.WriteLine($"🔍 [SERVICE] TESTANDO PDF: {blobItem.Name}");
                    _logger.LogInformation($"🔍 [SERVICE] TESTANDO PDF: {blobItem.Name}");

                    var resultado = await GetSpecificPDFExtractedData(blobItem.Name);

                    // Resumo do resultado
                    var resumo = new
                    {
                        nomeArquivo = blobItem.Name,
                        isCrayonPDF = resultado.IsCrayonPDF,
                        temErro = resultado.TemErro,
                        mensagemErro = resultado.MensagemErro,
                        totalDadosExtraidos = resultado.DadosExtraidos?.Count ?? 0,
                        dadosExtraidos = resultado.DadosExtraidos?.Select(d => new
                        {
                            cliente = d.Cliente,
                            descricaoServico = d.DescricaoServico,
                            dataInicio = d.DataInicio.ToString("yyyy-MM-dd"),
                            dataFim = d.DataFim.ToString("yyyy-MM-dd"),
                            preco = d.Preco,
                            extraidoComSucesso = d.ExtraidoComSucesso,
                            mensagemErro = d.MensagemErro
                        }).ToList()
                    };

                    resultados.Add(resumo);

                    Console.WriteLine($"✅ [SERVICE] PDF {blobItem.Name}: {resumo.totalDadosExtraidos} dados extraídos");
                    _logger.LogInformation($"✅ [SERVICE] PDF {blobItem.Name}: {resumo.totalDadosExtraidos} dados extraídos");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ [SERVICE] ERRO no PDF {blobItem.Name}: {ex.Message}");
                    _logger.LogError(ex, $"❌ [SERVICE] ERRO no PDF {blobItem.Name}");

                    resultados.Add(new
                    {
                        nomeArquivo = blobItem.Name,
                        isCrayonPDF = false,
                        temErro = true,
                        mensagemErro = ex.Message,
                        totalDadosExtraidos = 0,
                        dadosExtraidos = new List<object>()
                    });
                }
            }

            Console.WriteLine($"🚨🚨🚨 [SERVICE] TESTE COMPLETO: {resultados.Count} PDFs testados");
            _logger.LogInformation($"🚨🚨🚨 [SERVICE] TESTE COMPLETO: {resultados.Count} PDFs testados");

            return resultados;
        }

        /// <summary>
        /// Obtém os dados extraídos de todos os PDFs Crayon no container
        /// </summary>
        public async Task<Dictionary<string, CrayonPDFExtractionResult>> GetCrayonPDFsExtractedData()
        {
            var result = new Dictionary<string, CrayonPDFExtractionResult>();

            try
            {
                var containerClient = _blobServiceClient.GetBlobContainerClient(_containerName);

                await foreach (var blobItem in containerClient.GetBlobsAsync())
                {
                    if (!blobItem.Name.EndsWith(".pdf", StringComparison.OrdinalIgnoreCase))
                        continue;

                    try
                    {
                        var blobClient = containerClient.GetBlobClient(blobItem.Name);

                        using var memoryStream = new MemoryStream();
                        await blobClient.DownloadToAsync(memoryStream);
                        memoryStream.Position = 0;

                        var extractionResult = ExtractCrayonData(blobItem.Name, memoryStream);

                        if (extractionResult.IsCrayonPDF)
                        {
                            result[blobItem.Name] = extractionResult;
                            _logger.LogInformation($"Dados extraídos do PDF Crayon: {blobItem.Name}");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"Erro ao extrair dados do arquivo {blobItem.Name}");
                        result[blobItem.Name] = new CrayonPDFExtractionResult
                        {
                            NomeArquivo = blobItem.Name,
                            IsCrayonPDF = false,
                            TemErro = true,
                            MensagemErro = ex.Message
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao processar PDFs do container");
                throw;
            }

            return result;
        }

        /// <summary>
        /// Extrai texto de PDF usando DocNet
        /// </summary>
        private string ExtractTextWithDocNet(MemoryStream pdfStream)
        {
            try
            {
                using var docReader = DocLib.Instance.GetDocReader(pdfStream.ToArray(), new PageDimensions(1080, 1920));
                var text = new StringBuilder();

                for (int i = 0; i < docReader.GetPageCount(); i++)
                {
                    using var pageReader = docReader.GetPageReader(i);
                    var pageText = pageReader.GetText();
                    text.AppendLine(pageText);
                }

                return text.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao extrair texto com DocNet");
                throw;
            }
        }

        /// <summary>
        /// Verifica se o PDF é do tipo Crayon
        /// </summary>
        private bool IsCrayonPDF(string textoCompleto)
        {
            if (string.IsNullOrEmpty(textoCompleto))
                return false;

            // === EXCLUSÕES PRIMEIRO - PDFs que NÃO são Crayon ===
            var indicadoresExclusao = new[]
            {
                "ARROWECS PORTUGAL",
                "Arrow",
                "<EMAIL>",
                "www.arrowecs.pt",
                "ARROW ECS"
            };

            // Se contém indicadores de exclusão, NÃO é Crayon
            if (indicadoresExclusao.Any(indicador =>
                textoCompleto.Contains(indicador, StringComparison.OrdinalIgnoreCase)))
            {
                return false;
            }

            // === INDICADORES CRAYON ===
            var indicadoresCrayon = new[]
            {
                // Indicadores principais (mais específicos)
                "CRAYON SOFTWARE LICENSING",
                "Crayon Software Licensing",
                "<EMAIL>",
                "<EMAIL>",

                // Indicadores de estrutura específica Crayon
                "CSP-AZURE-PLAN",
                "Azure plan Consumption Period",
                "Microsoft CSP Azure Plan",

                // Indicadores de formato Crayon
                "PCE", // Unidade específica dos PDFs Crayon
                "Agreement number",
                "Publisher: Microsoft",
                "ATCUD:", // Código específico português nos PDFs Crayon

                // Estrutura de endereço Crayon
                "AV DA REPÚBLICA , 50 - 10",
                "1069-211 Lisboa"
            };

            // Verificar se pelo menos 1 indicador Crayon está presente
            return indicadoresCrayon.Any(indicador =>
                textoCompleto.Contains(indicador, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// Extrai dados completos de um PDF Crayon
        /// </summary>
        private CrayonPDFExtractionResult ExtractCrayonData(string nomeArquivo, MemoryStream pdfStream)
        {
            var result = new CrayonPDFExtractionResult
            {
                NomeArquivo = nomeArquivo
            };

            try
            {
                result.TextoBruto = ExtractTextWithDocNet(pdfStream);
                result.IsCrayonPDF = IsCrayonPDF(result.TextoBruto);

                if (!result.IsCrayonPDF)
                {
                    result.MensagemErro = "PDF não identificado como Crayon";
                    return result;
                }

                // Extrair dados específicos
                var dadosExtraidos = ExtractDataFromText(result.TextoBruto);
                result.DadosExtraidos = dadosExtraidos;

                if (!dadosExtraidos.Any())
                {
                    result.TemErro = true;
                    result.MensagemErro = "Nenhum dado foi extraído do PDF";
                }
            }
            catch (Exception ex)
            {
                result.TemErro = true;
                result.MensagemErro = ex.Message;
                _logger.LogError(ex, $"Erro ao extrair dados do PDF {nomeArquivo}");
            }

            return result;
        }

        /// <summary>
        /// Extrai dados específicos do texto do PDF
        /// </summary>
        private List<CrayonExtractedData> ExtractDataFromText(string textoCompleto)
        {
            var dadosExtraidos = new List<CrayonExtractedData>();

            try
            {
                // 1. Extrair cliente (procurar por "BI4ALL Consultores de Gestão, Lda")
                var cliente = ExtractCliente(textoCompleto);

                // 2. Extrair produtos/serviços com valores E períodos específicos
                var produtos = ExtractProdutosComPeriodos(textoCompleto);

                foreach (var produto in produtos)
                {
                    dadosExtraidos.Add(new CrayonExtractedData
                    {
                        Cliente = cliente,
                        DescricaoServico = produto.Descricao,
                        DataInicio = produto.DataInicio,
                        DataFim = produto.DataFim,
                        Preco = produto.Preco,
                        Iva = produto.Iva,
                        Total = produto.Preco + produto.Iva,
                        ExtraidoComSucesso = true
                    });
                }

                if (!dadosExtraidos.Any())
                {
                    // Se não conseguiu extrair produtos, criar entrada com erro
                    dadosExtraidos.Add(new CrayonExtractedData
                    {
                        Cliente = cliente,
                        ExtraidoComSucesso = false,
                        MensagemErro = "Não foi possível extrair produtos/serviços do PDF"
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao extrair dados do texto");
                dadosExtraidos.Add(new CrayonExtractedData
                {
                    ExtraidoComSucesso = false,
                    MensagemErro = ex.Message
                });
            }

            return dadosExtraidos;
        }

        /// <summary>
        /// Extrai o nome do cliente (procura por "BI4ALL Consultores de Gestão, Lda")
        /// </summary>
        private string ExtractCliente(string textoCompleto)
        {
            // Procurar diretamente pelo nome esperado
            if (textoCompleto.Contains("BI4ALL Consultores de Gestão, Lda", StringComparison.OrdinalIgnoreCase))
            {
                return "BI4ALL Consultores de Gestão, Lda";
            }

            // Padrões alternativos próximos de "Receiver"
            var padroes = new[]
            {
                new Regex(@"Receiver[:\s]*\n?\s*([^\n\r]+)", RegexOptions.IgnoreCase),
                new Regex(@"Bill\s+To[:\s]*\n?\s*([^\n\r]+)", RegexOptions.IgnoreCase),
                new Regex(@"BI4ALL[^\n\r]*", RegexOptions.IgnoreCase)
            };

            foreach (var padrao in padroes)
            {
                var match = padrao.Match(textoCompleto);
                if (match.Success)
                {
                    var cliente = match.Groups[1].Value.Trim();
                    if (!string.IsNullOrWhiteSpace(cliente))
                    {
                        return cliente;
                    }
                }
            }

            return "Cliente não identificado";
        }

        /// <summary>
        /// Extrai período de serviço ou usa data da fatura para criar período mensal - versão robusta
        /// </summary>
        private (DateTime dataInicio, DateTime dataFim) ExtractPeriodo(string textoCompleto)
        {
            // Múltiplos padrões para diferentes formatos de período
            var padroesPeriodo = new[]
            {
                // Formato: "Period: YYYY-MM-DD - YYYY-MM-DD"
                new Regex(@"Period:\s*(\d{4}-\d{2}-\d{2})\s*-\s*(\d{4}-\d{2}-\d{2})", RegexOptions.IgnoreCase),

                // Formato: "Consumption Period: YYYY-MM-DD - YYYY-MM-DD"
                new Regex(@"Consumption\s+Period:\s*(\d{4}-\d{2}-\d{2})\s*-\s*(\d{4}-\d{2}-\d{2})", RegexOptions.IgnoreCase),

                // Formato: "Start Date: YYYY-MM-DD, End date: YYYY-MM-DD"
                new Regex(@"Start\s+Date:\s*(\d{4}-\d{2}-\d{2}).*?End\s+date:\s*(\d{4}-\d{2}-\d{2})", RegexOptions.IgnoreCase | RegexOptions.Singleline),

                // Formato: "YYYY-MM-DD - YYYY-MM-DD" (genérico)
                new Regex(@"(\d{4}-\d{2}-\d{2})\s*-\s*(\d{4}-\d{2}-\d{2})", RegexOptions.IgnoreCase)
            };

            foreach (var padrao in padroesPeriodo)
            {
                var match = padrao.Match(textoCompleto);
                if (match.Success)
                {
                    if (DateTime.TryParse(match.Groups[1].Value, out DateTime inicio) &&
                        DateTime.TryParse(match.Groups[2].Value, out DateTime fim))
                    {
                        return (inicio, fim);
                    }
                }
            }

            // Se não encontrou período explícito, procurar por data da fatura
            var padroesFatura = new[]
            {
                new Regex(@"Invoice\s+Date\s*(\d{1,2}[./\-]\d{1,2}[./\-]\d{4})", RegexOptions.IgnoreCase),
                new Regex(@"Invoice\s+Date\s*(\d{4}-\d{2}-\d{2})", RegexOptions.IgnoreCase),
                new Regex(@"Data\s+da\s+Fatura[:\s]*(\d{1,2}[./\-]\d{1,2}[./\-]\d{4})", RegexOptions.IgnoreCase)
            };

            foreach (var padrao in padroesFatura)
            {
                var match = padrao.Match(textoCompleto);
                if (match.Success && TryParseDate(match.Groups[1].Value, out DateTime dataFatura))
                {
                    // Criar período mensal baseado na data da fatura
                    var inicioMes = new DateTime(dataFatura.Year, dataFatura.Month, 1);
                    var fimMes = inicioMes.AddMonths(1).AddDays(-1);
                    return (inicioMes, fimMes);
                }
            }

            // Fallback: usar mês atual
            var hoje = DateTime.Now;
            var inicioAtual = new DateTime(hoje.Year, hoje.Month, 1);
            var fimAtual = inicioAtual.AddMonths(1).AddDays(-1);
            return (inicioAtual, fimAtual);
        }

        /// <summary>
        /// Extrai período baseado na data da fatura (fallback quando não há período explícito)
        /// </summary>
        private (DateTime dataInicio, DateTime dataFim) ExtractPeriodoFromInvoiceDate(string textoCompleto)
        {
            // EXTRAIR APENAS A DATA DA FATURA - NÃO PERÍODOS DE PRODUTOS!
            // Este método deve ser usado apenas como fallback quando não há período específico

            // PROCURAR APENAS PELA DATA DA FATURA (não por períodos de produtos)
            var padroesFatura = new[]
            {
                // Formato novo: "2025-04-22"
                new Regex(@"Invoice\s+Date\s*(\d{4}-\d{2}-\d{2})", RegexOptions.IgnoreCase),

                // Datas no formato dd.MM.yyyy
                new Regex(@"(\d{1,2}\.\d{1,2}\.\d{4})"),

                // Formato yyyy-MM-dd genérico
                new Regex(@"(\d{4}-\d{2}-\d{2})")
            };

            foreach (var padrao in padroesFatura)
            {
                var matches = padrao.Matches(textoCompleto);
                foreach (Match match in matches)
                {
                    var dataStr = match.Groups[1].Value;

                    // Tentar formato yyyy-MM-dd primeiro
                    if (DateTime.TryParseExact(dataStr, "yyyy-MM-dd", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime dataFatura))
                    {
                        // Criar período mensal baseado na data da fatura
                        var inicioMes = new DateTime(dataFatura.Year, dataFatura.Month, 1);
                        var fimMes = inicioMes.AddMonths(1).AddDays(-1);
                        return (inicioMes, fimMes);
                    }
                    // Tentar formato dd.MM.yyyy
                    else if (DateTime.TryParseExact(dataStr, "dd.MM.yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out dataFatura))
                    {
                        // Criar período mensal baseado na data da fatura
                        var inicioMes = new DateTime(dataFatura.Year, dataFatura.Month, 1);
                        var fimMes = inicioMes.AddMonths(1).AddDays(-1);
                        return (inicioMes, fimMes);
                    }
                }
            }

            // 3. FALLBACK: usar mês atual
            var hoje = DateTime.Now;
            var inicioFallback = new DateTime(hoje.Year, hoje.Month, 1);
            var fimFallback = inicioFallback.AddMonths(1).AddDays(-1);
            return (inicioFallback, fimFallback);
        }

        /// <summary>
        /// Extrai período específico para um produto individual - VERSÃO MELHORADA
        /// </summary>
        private (DateTime dataInicio, DateTime dataFim) ExtractPeriodoEspecificoProduto(string[] linhas, int indiceLinha, DateTime fallbackInicio, DateTime fallbackFim)
        {
            // NOVA ESTRATÉGIA ROBUSTA: MAPEAMENTO DE PERÍODOS
            // Esta abordagem funciona para TODOS os formatos de PDF (2023, 2024, 2025, especiais)

            var periodoEspecifico = ExtractPeriodoComMapeamento(linhas, indiceLinha, fallbackInicio, fallbackFim);

            return periodoEspecifico;
        }

        /// <summary>
        /// NOVA ESTRATÉGIA: Extrai período usando mapeamento robusto
        /// Funciona para TODOS os formatos de PDF (2023, 2024, 2025, especiais)
        /// </summary>
        private (DateTime dataInicio, DateTime dataFim) ExtractPeriodoComMapeamento(string[] linhas, int indiceLinha, DateTime fallbackInicio, DateTime fallbackFim)
        {
            // PASSO 1: EXTRAIR TODOS OS PERÍODOS DO TEXTO
            var periodosEncontrados = ExtrairTodosPeriodosDoTexto(linhas);

            // PASSO 2: ENCONTRAR O PERÍODO MAIS PRÓXIMO DESTA LINHA DE PRODUTO
            var periodoMaisProximo = EncontrarPeriodoMaisProximo(periodosEncontrados, indiceLinha, linhas);

            // PASSO 3: RETORNAR PERÍODO ESPECÍFICO OU FALLBACK
            if (periodoMaisProximo != null)
            {
                return (periodoMaisProximo.DataInicio, periodoMaisProximo.DataFim);
            }

            // FALLBACK: Usar invoice date se não encontrou período específico
            return (fallbackInicio, fallbackFim);
        }

        /// <summary>
        /// Classe para mapear períodos encontrados no texto
        /// </summary>
        private class PeriodoEncontrado
        {
            public DateTime DataInicio { get; set; }
            public DateTime DataFim { get; set; }
            public int LinhaEncontrada { get; set; }
            public string TipoOrigem { get; set; } // "Azure Plan", "Power BI Pro", etc.
            public string TextoOriginal { get; set; }
        }

        /// <summary>
        /// Extrai TODOS os períodos específicos encontrados no texto
        /// </summary>
        private List<PeriodoEncontrado> ExtrairTodosPeriodosDoTexto(string[] linhas)
        {
            var periodosEncontrados = new List<PeriodoEncontrado>();

            // PADRÕES PARA TODOS OS TIPOS DE PERÍODO
            var padroesPeriodo = new[]
            {
                // === AZURE PLAN PATTERNS ===
                // Formato padrão: "Azure plan Consumption Period: 2024-03-01 - 2024-03-31"
                new Regex(@"Azure\s+plan\s+Consumption\s+Period:\s*(\d{4}-\d{2}-\d{2})\s*-\s*(\d{4}-\d{2}-\d{2})", RegexOptions.IgnoreCase),

                // Formato com quebra de linha: "Azure plan Consumption Period: 2023-08-01 -\n2023-08-31"
                new Regex(@"Azure\s+plan\s+Consumption\s+Period:\s*(\d{4}-\d{2}-\d{2})\s*-\s*(\d{4}-\d{2}-\d{2})", RegexOptions.IgnoreCase | RegexOptions.Singleline),

                // === POWER BI PRO PATTERNS ===
                // Formato especial: "Start Date: 2025-03-30, End date: 2026-03-29"
                new Regex(@"Start\s+Date:\s*(\d{4}-\d{2}-\d{2}),?\s*End\s+date:\s*(\d{4}-\d{2}-\d{2})", RegexOptions.IgnoreCase),

                // === CONSUMPTION PERIOD GENÉRICO ===
                // Formato: "Consumption Period: 2024-01-01 - 2024-01-31"
                new Regex(@"Consumption\s+Period:\s*(\d{4}-\d{2}-\d{2})\s*-\s*(\d{4}-\d{2}-\d{2})", RegexOptions.IgnoreCase),

                // === OUTROS PADRÕES ===
                // Data de disposição: "colocados à disposição na data 2024-04-11"
                new Regex(@"colocados\s+à\s+disposição\s+na\s+data\s+(\d{4}-\d{2}-\d{2})", RegexOptions.IgnoreCase)
            };

            // PROCURAR EM TODAS AS LINHAS
            for (int i = 0; i < linhas.Length; i++)
            {
                var linha = linhas[i].Trim();

                // Juntar com linhas seguintes para capturar períodos quebrados
                var contexto = linha;
                for (int j = 1; j <= 3 && i + j < linhas.Length; j++)
                {
                    contexto += " " + linhas[i + j].Trim();
                }

                foreach (var padrao in padroesPeriodo)
                {
                    var match = padrao.Match(contexto);
                    if (match.Success)
                    {
                        if (match.Groups.Count >= 3) // Período completo (início e fim)
                        {
                            if (DateTime.TryParse(match.Groups[1].Value, out DateTime inicio) &&
                                DateTime.TryParse(match.Groups[2].Value, out DateTime fim))
                            {
                                var tipoOrigem = DeterminarTipoOrigem(contexto);
                                periodosEncontrados.Add(new PeriodoEncontrado
                                {
                                    DataInicio = inicio,
                                    DataFim = fim,
                                    LinhaEncontrada = i,
                                    TipoOrigem = tipoOrigem,
                                    TextoOriginal = match.Value
                                });
                            }
                        }
                        else if (match.Groups.Count >= 2) // Data única (disposição)
                        {
                            if (DateTime.TryParse(match.Groups[1].Value, out DateTime dataDisposicao))
                            {
                                var inicioMes = new DateTime(dataDisposicao.Year, dataDisposicao.Month, 1);
                                var fimMes = inicioMes.AddMonths(1).AddDays(-1);

                                periodosEncontrados.Add(new PeriodoEncontrado
                                {
                                    DataInicio = inicioMes,
                                    DataFim = fimMes,
                                    LinhaEncontrada = i,
                                    TipoOrigem = "Data Disposição",
                                    TextoOriginal = match.Value
                                });
                            }
                        }
                    }
                }
            }

            return periodosEncontrados;
        }

        /// <summary>
        /// Determina o tipo de origem do período baseado no contexto
        /// </summary>
        private string DeterminarTipoOrigem(string contexto)
        {
            if (contexto.Contains("Azure plan Consumption Period", StringComparison.OrdinalIgnoreCase))
                return "Azure Plan";

            if (contexto.Contains("Start Date", StringComparison.OrdinalIgnoreCase) &&
                contexto.Contains("End date", StringComparison.OrdinalIgnoreCase))
                return "Power BI Pro";

            if (contexto.Contains("Consumption Period", StringComparison.OrdinalIgnoreCase))
                return "Consumption Period";

            if (contexto.Contains("colocados à disposição", StringComparison.OrdinalIgnoreCase))
                return "Data Disposição";

            return "Período Genérico";
        }

        /// <summary>
        /// Encontra o período mais próximo da linha do produto - VERSÃO MELHORADA PARA MÚLTIPLOS PERÍODOS
        /// </summary>
        private PeriodoEncontrado EncontrarPeriodoMaisProximo(List<PeriodoEncontrado> periodosEncontrados, int linhaProduto, string[] linhas)
        {
            if (periodosEncontrados == null || !periodosEncontrados.Any())
                return null;

            Console.WriteLine($"🔍 [MÚLTIPLOS PERÍODOS] Analisando linha {linhaProduto} com {periodosEncontrados.Count} períodos disponíveis");
            _logger.LogInformation($"🔍 [MÚLTIPLOS PERÍODOS] Analisando linha {linhaProduto} com {periodosEncontrados.Count} períodos disponíveis");

            // 1. VERIFICAR SE É ESPECIFICAMENTE O AZURE PLAN
            if (EhLinhaDoAzurePlan(linhaProduto, linhas))
            {
                // Azure Plan: usar período específico do Azure Plan
                var azurePlanPeriodo = periodosEncontrados
                    .Where(p => p.TipoOrigem == "Azure Plan")
                    .FirstOrDefault();

                if (azurePlanPeriodo != null)
                {
                    Console.WriteLine($"🎯 [AZURE PLAN] Linha {linhaProduto}: Usando período específico {azurePlanPeriodo.DataInicio:yyyy-MM-dd} a {azurePlanPeriodo.DataFim:yyyy-MM-dd}");
                    _logger.LogInformation($"🎯 AZURE PLAN IDENTIFICADO na linha {linhaProduto}: Usando período específico {azurePlanPeriodo.DataInicio:yyyy-MM-dd} a {azurePlanPeriodo.DataFim:yyyy-MM-dd}");
                    return azurePlanPeriodo;
                }
            }

            // 2. NOVA LÓGICA: PRODUTOS NÃO-AZURE COM MÚLTIPLOS PERÍODOS
            // Se há múltiplos períodos no PDF, tentar mapear ao período mais próximo
            if (periodosEncontrados.Count > 1)
            {
                Console.WriteLine($"📊 [MÚLTIPLOS PERÍODOS] Detectados {periodosEncontrados.Count} períodos - tentando mapear produto não-Azure");
                _logger.LogInformation($"📊 [MÚLTIPLOS PERÍODOS] Detectados {periodosEncontrados.Count} períodos - tentando mapear produto não-Azure");

                // Listar todos os períodos encontrados
                foreach (var periodo in periodosEncontrados)
                {
                    Console.WriteLine($"   📅 Período na linha {periodo.LinhaEncontrada}: {periodo.DataInicio:yyyy-MM-dd} a {periodo.DataFim:yyyy-MM-dd} ({periodo.TipoOrigem})");
                    _logger.LogInformation($"   📅 Período na linha {periodo.LinhaEncontrada}: {periodo.DataInicio:yyyy-MM-dd} a {periodo.DataFim:yyyy-MM-dd} ({periodo.TipoOrigem})");
                }

                // Encontrar o período mais próximo por proximidade de linha
                var periodoMaisProximo = periodosEncontrados
                    .OrderBy(p => Math.Abs(p.LinhaEncontrada - linhaProduto))
                    .First();

                // Verificar se a proximidade é razoável (não muito longe)
                int distancia = Math.Abs(periodoMaisProximo.LinhaEncontrada - linhaProduto);
                if (distancia <= 20) // Máximo 20 linhas de distância
                {
                    Console.WriteLine($"🎯 [MÚLTIPLOS PERÍODOS] Linha {linhaProduto} → Período mais próximo: linha {periodoMaisProximo.LinhaEncontrada} (distância: {distancia}) - {periodoMaisProximo.DataInicio:yyyy-MM-dd} a {periodoMaisProximo.DataFim:yyyy-MM-dd}");
                    _logger.LogInformation($"🎯 [MÚLTIPLOS PERÍODOS] Linha {linhaProduto} → Período mais próximo: linha {periodoMaisProximo.LinhaEncontrada} (distância: {distancia}) - {periodoMaisProximo.DataInicio:yyyy-MM-dd} a {periodoMaisProximo.DataFim:yyyy-MM-dd}");
                    return periodoMaisProximo;
                }
                else
                {
                    Console.WriteLine($"⚠️ [MÚLTIPLOS PERÍODOS] Período mais próximo muito longe (distância: {distancia}) - usando fallback");
                    _logger.LogWarning($"⚠️ [MÚLTIPLOS PERÍODOS] Período mais próximo muito longe (distância: {distancia}) - usando fallback");
                }
            }

            // 3. FALLBACK: Um único período ou nenhum período próximo
            Console.WriteLine($"📅 [FALLBACK] Produto não-Azure na linha {linhaProduto}: Usando fallback da invoice date");
            _logger.LogInformation($"📅 PRODUTO NÃO-AZURE na linha {linhaProduto}: Usando fallback da invoice date");
            return null; // Retorna null para usar fallback
        }

        /// <summary>
        /// Verifica se a linha pertence especificamente ao Azure Plan - VERSÃO CORRIGIDA PARA 2023
        /// </summary>
        private bool EhLinhaDoAzurePlan(int linhaProduto, string[] linhas)
        {
            try
            {
                // LÓGICA CORRIGIDA: Diferentes estruturas para PDFs 2023 vs 2024/2025
                // PDF 2023: CSP-AZURE-PLAN -> Azure plan Consumption Period -> linha de produto
                // PDF 2024/2025: linha de produto -> CSP-AZURE-PLAN -> Azure plan Consumption Period

                _logger.LogInformation($"🔍 Verificando se linha {linhaProduto} é Azure Plan: '{linhas[linhaProduto]?.Trim()}'");

                // PASSO 1: Procurar CSP-AZURE-PLAN tanto ANTES quanto DEPOIS da linha de produto
                int linhaCSPAzurePlan = -1;

                // Primeiro procurar DEPOIS (PDFs 2024/2025)
                for (int k = 1; k <= 3; k++)
                {
                    int linhaIndex = linhaProduto + k;
                    if (linhaIndex >= 0 && linhaIndex < linhas.Length)
                    {
                        var linha = linhas[linhaIndex].Trim();
                        if (linha.Equals("CSP-AZURE-PLAN", StringComparison.OrdinalIgnoreCase))
                        {
                            linhaCSPAzurePlan = linhaIndex;
                            _logger.LogInformation($"✅ Encontrado CSP-AZURE-PLAN na linha {linhaIndex} (DEPOIS da linha produto {linhaProduto})");
                            break;
                        }
                    }
                }

                // Se não encontrou depois, procurar ANTES (PDFs 2023)
                if (linhaCSPAzurePlan == -1)
                {
                    for (int k = 1; k <= 10; k++) // Procurar mais longe para PDFs 2023
                    {
                        int linhaIndex = linhaProduto - k;
                        if (linhaIndex >= 0 && linhaIndex < linhas.Length)
                        {
                            var linha = linhas[linhaIndex].Trim();
                            if (linha.Equals("CSP-AZURE-PLAN", StringComparison.OrdinalIgnoreCase))
                            {
                                linhaCSPAzurePlan = linhaIndex;
                                _logger.LogInformation($"✅ Encontrado CSP-AZURE-PLAN na linha {linhaIndex} (ANTES da linha produto {linhaProduto})");
                                break;
                            }
                        }
                    }
                }

                if (linhaCSPAzurePlan == -1)
                {
                    _logger.LogInformation($"❌ CSP-AZURE-PLAN não encontrado nas proximidades da linha {linhaProduto}");
                    return false;
                }

                // PASSO 2: Verificar se há "Azure plan Consumption Period" nas proximidades do CSP-AZURE-PLAN
                int linhaPeriodoAzure = -1;
                for (int k = 1; k <= 5; k++) // Procurar nas 5 linhas APÓS CSP-AZURE-PLAN
                {
                    int linhaIndex = linhaCSPAzurePlan + k;
                    if (linhaIndex >= 0 && linhaIndex < linhas.Length)
                    {
                        var linha = linhas[linhaIndex].Trim();
                        if (linha.Contains("Azure plan Consumption Period", StringComparison.OrdinalIgnoreCase))
                        {
                            linhaPeriodoAzure = linhaIndex;
                            _logger.LogInformation($"✅ Encontrado Azure plan Consumption Period na linha {linhaIndex}");
                            break;
                        }
                    }
                }

                if (linhaPeriodoAzure == -1)
                {
                    _logger.LogInformation($"❌ Azure plan Consumption Period não encontrado após CSP-AZURE-PLAN");
                    return false;
                }

                // PASSO 3: VERIFICAÇÃO BASEADA NA ESTRUTURA DO PDF
                bool ehAzurePlan2023 = false;
                bool ehAzurePlan2024 = false;

                if (linhaCSPAzurePlan < linhaProduto)
                {
                    // PDF 2023: CSP-AZURE-PLAN vem ANTES da linha de produto
                    // Verificar se a linha de produto está depois do período
                    ehAzurePlan2023 = (linhaProduto > linhaPeriodoAzure) && (linhaProduto - linhaPeriodoAzure <= 5);
                    _logger.LogInformation($"📋 Estrutura PDF 2023 detectada: CSP-AZURE-PLAN({linhaCSPAzurePlan}) -> Período({linhaPeriodoAzure}) -> Produto({linhaProduto})");
                }
                else
                {
                    // PDF 2024/2025: linha de produto vem ANTES do CSP-AZURE-PLAN
                    ehAzurePlan2024 = (linhaProduto < linhaCSPAzurePlan) && (linhaCSPAzurePlan - linhaProduto == 1);
                    _logger.LogInformation($"📋 Estrutura PDF 2024/2025 detectada: Produto({linhaProduto}) -> CSP-AZURE-PLAN({linhaCSPAzurePlan}) -> Período({linhaPeriodoAzure})");

                    // Verificação adicional para PDFs 2024/2025: não deve haver outras linhas de produto intermediárias
                    for (int i = linhaProduto + 1; i < linhaCSPAzurePlan; i++)
                    {
                        var linhaIntermedia = linhas[i].Trim();
                        if (linhaIntermedia.Contains("PCE") && Regex.IsMatch(linhaIntermedia, @"\d+\.\d+"))
                        {
                            _logger.LogInformation($"❌ Encontrada linha de produto intermediária na linha {i}: '{linhaIntermedia}'");
                            return false;
                        }
                    }
                }

                bool resultado = ehAzurePlan2023 || ehAzurePlan2024;

                _logger.LogInformation($"🎯 Verificação final linha {linhaProduto}: 2023={ehAzurePlan2023}, 2024={ehAzurePlan2024}, Resultado={resultado}");
                _logger.LogInformation($"📊 Posições: Produto={linhaProduto}, CSP-AZURE-PLAN={linhaCSPAzurePlan}, Período={linhaPeriodoAzure}");

                return resultado;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erro ao verificar se linha {linhaProduto} é Azure Plan");
                return false;
            }
        }



        /// <summary>
        /// Tenta fazer parse de uma data em vários formatos
        /// </summary>
        private bool TryParseDate(string dateStr, out DateTime date)
        {
            date = DateTime.MinValue;

            if (string.IsNullOrWhiteSpace(dateStr))
                return false;

            // TESTE DIRETO PRIMEIRO - formato português dd.MM.yyyy
            if (DateTime.TryParseExact(dateStr.Trim(), "dd.MM.yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out date))
            {
                return true;
            }

            // Outros formatos como fallback
            var formats = new[]
            {
                "d.M.yyyy", "dd.M.yyyy", "d.MM.yyyy",
                "dd/MM/yyyy", "dd-MM-yyyy",
                "MM.dd.yyyy", "MM/dd/yyyy", "MM-dd-yyyy",
                "yyyy.MM.dd", "yyyy/MM/dd", "yyyy-MM-dd"
            };

            foreach (var format in formats)
            {
                if (DateTime.TryParseExact(dateStr.Trim(), format, CultureInfo.InvariantCulture, DateTimeStyles.None, out date))
                {
                    return true;
                }
            }

            // Último recurso
            return DateTime.TryParse(dateStr.Trim(), out date);
        }

        /// <summary>
        /// Extrai produtos/serviços com preços E períodos específicos - versão robusta para múltiplos formatos
        /// </summary>
        private List<(string Descricao, decimal Preco, decimal Iva, DateTime DataInicio, DateTime DataFim)> ExtractProdutosComPeriodos(string textoCompleto)
        {
            var produtos = new List<(string Descricao, decimal Preco, decimal Iva, DateTime DataInicio, DateTime DataFim)>();
            bool azurePlanJaUsado = false; // Controlo para usar Azure Plan apenas uma vez

            try
            {
                // Dividir o texto em linhas para processamento sequencial
                var linhas = textoCompleto.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);

                // Extrair data da fatura como fallback
                var (dataFaturaInicio, dataFaturaFim) = ExtractPeriodoFromInvoiceDate(textoCompleto);

                for (int i = 0; i < linhas.Length; i++)
                {
                    var linha = linhas[i].Trim();

                    // Múltiplos padrões para diferentes formatos de PDF Crayon
                    // ORDEM IMPORTANTE: Padrões atuais (2024-2025) primeiro, depois padrões legados (2023)
                    var padroes = new[]
                    {
                        // === PADRÕES ATUAIS (2024-2025) - TESTADOS PRIMEIRO ===

                        // Formato 1: "1.00 PCE 257.76 23.00 59.29 257.76" (pontos decimais)
                        new Regex(@"^(\d+[.,]?\d*)\s+(\w+)\s+([\d.,]+)\s+([\d.,]+)\s+([\d.,]+)\s+([\d.,]+)$"),

                        // Formato 2: "CSP-AZURE-PLAN Microsoft CSP Azure Plan 1.00 ea 6,552.99 23.00 1,507.19 6,552.99"
                        new Regex(@"^([A-Z][A-Z0-9\-_]*)\s+(.+?)\s+(\d+[.,]?\d*)\s+(\w+)\s+([\d.,]+)\s+([\d.,]+)\s+([\d.,]+)\s+([\d.,]+)$"),

                        // Formato 3: Apenas valores numéricos em linha (mais flexível)
                        new Regex(@"^(\d+[.,]?\d*)\s+(\w+)\s+([\d.,]+)\s+([\d.,]+)\s+([\d.,]+)$"),

                        // Formato 4: Para vírgulas como separadores de milhares "1.00 PCE 6,115.03 23.00 1,406.46 6,115.03"
                        new Regex(@"^(\d+[.,]?\d*)\s+(\w+)\s+([\d,]+[.,]?\d*)\s+([\d,]+[.,]?\d*)\s+([\d,]+[.,]?\d*)\s+([\d,]+[.,]?\d*)$"),

                        // === PADRÕES LEGADOS (2023) - TESTADOS POR ÚLTIMO ===

                        // Formato 2023-1: "1,00 Pce 5 887,76 23.00 1 354,18 5 887,76" (espaços nos números)
                        new Regex(@"^(\d+,\d+)\s+(\w+)\s+([\d\s]+,\d+)\s+([\d.,]+)\s+([\d\s]+,\d+)\s+([\d\s]+,\d+)$"),

                        // Formato 2023-2: Variação com espaços diferentes
                        new Regex(@"^(\d+,\d+)\s+(\w+)\s+([\d\s,]+)\s+([\d.,]+)\s+([\d\s,]+)\s+([\d\s,]+)$")
                    };

                    foreach (var padrao in padroes)
                    {
                        var match = padrao.Match(linha);
                        if (match.Success)
                        {
                            string descricao = "Produto não identificado";
                            decimal preco = 0;
                            decimal iva = 0;
                            DateTime dataInicio = dataFaturaInicio;
                            DateTime dataFim = dataFaturaFim;

                            // Processar baseado no padrão que fez match
                            if (padrao == padroes[1]) // Formato com código e descrição na mesma linha
                            {
                                var codigo = match.Groups[1].Value;
                                var desc = match.Groups[2].Value.Trim();
                                var precoStr = NormalizeDecimalValue(match.Groups[5].Value);
                                var ivaStr = NormalizeDecimalValue(match.Groups[7].Value);

                                descricao = $"{codigo} - {desc}";

                                decimal.TryParse(precoStr, NumberStyles.Any, CultureInfo.InvariantCulture, out preco);
                                decimal.TryParse(ivaStr, NumberStyles.Any, CultureInfo.InvariantCulture, out iva);
                            }
                            else // Formatos onde a descrição vem em linhas separadas
                            {
                                // SEMPRE usar os valores da linha atual que fez match
                                int totalGroups = match.Groups.Count;
                                string precoStr, ivaStr;

                                if (totalGroups >= 6) // Formato completo: qty unit price vat vatAmount total
                                {
                                    precoStr = NormalizeDecimalValue(match.Groups[3].Value); // price (posição 3)
                                    ivaStr = NormalizeDecimalValue(match.Groups[5].Value);   // vatAmount (posição 5)
                                }
                                else // Formato reduzido: qty unit price vat total
                                {
                                    precoStr = NormalizeDecimalValue(match.Groups[3].Value); // price (posição 3)
                                    ivaStr = NormalizeDecimalValue(match.Groups[4].Value);   // vat (posição 4)
                                }

                                decimal.TryParse(precoStr, NumberStyles.Any, CultureInfo.InvariantCulture, out preco);
                                decimal.TryParse(ivaStr, NumberStyles.Any, CultureInfo.InvariantCulture, out iva);

                                // Procurar descrição nas linhas adjacentes DESTA linha específica
                                descricao = FindProductDescription(linhas, i, azurePlanJaUsado);

                                // Se encontrou Azure Plan, marcar como usado
                                if (descricao.Contains("Azure Plan", StringComparison.OrdinalIgnoreCase))
                                {
                                    azurePlanJaUsado = true;
                                }
                            }

                            // Procurar período específico para este produto - USAR INVOICE DATE COMO FALLBACK
                            var (produtoInicio, produtoFim) = ExtractPeriodoEspecificoProduto(linhas, i, dataFaturaInicio, dataFaturaFim);
                            dataInicio = produtoInicio;
                            dataFim = produtoFim;

                            // Adicionar produto independentemente do valor (produtos gratuitos são válidos)
                            produtos.Add((CleanDescription(descricao), preco, iva, dataInicio, dataFim));
                            break; // Sair do loop de padrões se encontrou match
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao extrair produtos do texto");
            }

            // === GARANTIR QUE AZURE PLAN APAREÇA PRIMEIRO ===
            // Separar Azure Plan dos outros produtos
            var azurePlan = produtos.Where(p => p.Descricao.Contains("Azure Plan", StringComparison.OrdinalIgnoreCase)).ToList();
            var outrosProdutos = produtos.Where(p => !p.Descricao.Contains("Azure Plan", StringComparison.OrdinalIgnoreCase)).ToList();

            // Reordenar: Azure Plan primeiro, depois outros produtos
            var produtosOrdenados = new List<(string Descricao, decimal Preco, decimal Iva, DateTime DataInicio, DateTime DataFim)>();
            produtosOrdenados.AddRange(azurePlan);
            produtosOrdenados.AddRange(outrosProdutos);

            return produtosOrdenados;
        }

        /// <summary>
        /// Extrai produtos/serviços com preços - versão robusta para múltiplos formatos
        /// </summary>
        private List<(string Descricao, decimal Preco, decimal Iva)> ExtractProdutos(string textoCompleto)
        {
            var produtos = new List<(string Descricao, decimal Preco, decimal Iva)>();

            try
            {
                // Dividir o texto em linhas para processamento sequencial
                var linhas = textoCompleto.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);

                for (int i = 0; i < linhas.Length; i++)
                {
                    var linha = linhas[i].Trim();

                    // Múltiplos padrões para diferentes formatos de PDF Crayon
                    var padroes = new[]
                    {
                        // Formato 1: "1.00 PCE 257.76 23.00 59.29 257.76" (pontos decimais)
                        new Regex(@"^(\d+[.,]?\d*)\s+(\w+)\s+([\d.,]+)\s+([\d.,]+)\s+([\d.,]+)\s+([\d.,]+)$"),

                        // Formato 2: "CSP-AZURE-PLAN Microsoft CSP Azure Plan 1.00 ea 6,552.99 23.00 1,507.19 6,552.99"
                        new Regex(@"^([A-Z][A-Z0-9\-_]*)\s+(.+?)\s+(\d+[.,]?\d*)\s+(\w+)\s+([\d.,]+)\s+([\d.,]+)\s+([\d.,]+)\s+([\d.,]+)$"),

                        // Formato 3: Apenas valores numéricos em linha (mais flexível)
                        new Regex(@"^(\d+[.,]?\d*)\s+(\w+)\s+([\d.,]+)\s+([\d.,]+)\s+([\d.,]+)$"),

                        // Formato 4: Para vírgulas como separadores de milhares "1.00 PCE 6,115.03 23.00 1,406.46 6,115.03"
                        new Regex(@"^(\d+[.,]?\d*)\s+(\w+)\s+([\d,]+[.,]?\d*)\s+([\d,]+[.,]?\d*)\s+([\d,]+[.,]?\d*)\s+([\d,]+[.,]?\d*)$")
                    };

                    foreach (var padrao in padroes)
                    {
                        var match = padrao.Match(linha);
                        if (match.Success)
                        {
                            string descricao = "Produto não identificado";
                            decimal preco = 0;
                            decimal iva = 0;

                            // Processar baseado no padrão que fez match
                            if (padrao == padroes[1]) // Formato com código e descrição na mesma linha
                            {
                                var codigo = match.Groups[1].Value;
                                var desc = match.Groups[2].Value.Trim();
                                var precoStr = NormalizeDecimalValue(match.Groups[5].Value);
                                var ivaStr = NormalizeDecimalValue(match.Groups[7].Value);

                                descricao = $"{codigo} - {desc}";

                                decimal.TryParse(precoStr, NumberStyles.Any, CultureInfo.InvariantCulture, out preco);
                                decimal.TryParse(ivaStr, NumberStyles.Any, CultureInfo.InvariantCulture, out iva);
                            }
                            else // Formatos onde a descrição vem em linhas separadas
                            {
                                // SEMPRE usar os valores da linha atual que fez match
                                int totalGroups = match.Groups.Count;
                                string precoStr, ivaStr;

                                if (totalGroups >= 6) // Formato completo: qty unit price vat vatAmount total
                                {
                                    precoStr = NormalizeDecimalValue(match.Groups[3].Value); // price (posição 3)
                                    ivaStr = NormalizeDecimalValue(match.Groups[5].Value);   // vatAmount (posição 5)
                                }
                                else // Formato reduzido: qty unit price vat total
                                {
                                    precoStr = NormalizeDecimalValue(match.Groups[3].Value); // price (posição 3)
                                    ivaStr = NormalizeDecimalValue(match.Groups[4].Value);   // vat (posição 4)
                                }

                                decimal.TryParse(precoStr, NumberStyles.Any, CultureInfo.InvariantCulture, out preco);
                                decimal.TryParse(ivaStr, NumberStyles.Any, CultureInfo.InvariantCulture, out iva);

                                // Procurar descrição nas linhas adjacentes DESTA linha específica
                                descricao = FindProductDescription(linhas, i);
                            }

                            // Adicionar produto independentemente do valor (produtos gratuitos são válidos)
                            produtos.Add((CleanDescription(descricao), preco, iva));
                            break; // Sair do loop de padrões se encontrou match
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao extrair produtos do texto");
            }

            return produtos;
        }

        /// <summary>
        /// Procura a descrição do produto nas linhas adjacentes de forma mais precisa
        /// LÓGICA HÍBRIDA: Casos especiais + Descrições adjacentes + Controlo de duplicações
        /// </summary>
        private string FindProductDescription(string[] linhas, int indiceLinha, bool azurePlanJaUsado = false)
        {
            // === ESTRATÉGIA 0: CÓDIGOS ESPECÍFICOS CONHECIDOS (PRIORIDADE ABSOLUTA) ===
            // Verificar códigos específicos nas proximidades para casos especiais
            // APENAS para PDFs que têm CSP-AZURE-PLAN (para evitar afetar outros PDFs)
            bool temCSPAzurePlan = false;
            for (int check = Math.Max(0, indiceLinha - 5); check < Math.Min(indiceLinha + 5, linhas.Length); check++)
            {
                if (linhas[check].Trim().Equals("CSP-AZURE-PLAN", StringComparison.OrdinalIgnoreCase))
                {
                    temCSPAzurePlan = true;
                    break;
                }
            }

            if (temCSPAzurePlan)
            {
                for (int k = 1; k <= 3; k++)
                {
                    if (indiceLinha + k < linhas.Length)
                    {
                        var linhaSeguinte = linhas[indiceLinha + k].Trim();
                        if (linhaSeguinte.Equals("1358275", StringComparison.OrdinalIgnoreCase))
                        {
                            return "Power BI Pro"; // Código específico do Power BI Pro
                        }
                    }
                }
            }

            // === ESTRATÉGIA 1: DETECÇÃO DE FORMATO PDF === 
            // Detectar se é PDF 2023 (formato "Pce") ou 2024/2025 (formato "PCE")
            bool isPDF2023 = false;
            for (int check = Math.Max(0, indiceLinha - 2); check <= Math.Min(indiceLinha + 2, linhas.Length - 1); check++)
            {
                if (linhas[check].Contains(" Pce ")) // PDF 2023 usa "Pce"
                {
                    isPDF2023 = true;
                    break;
                }
            }

            if (isPDF2023)
            {
                // === LÓGICA PARA PDFs 2023 ===
                // Estrutura: Descrição → Valores → Código → Próxima Descrição
                // Procurar descrição ANTES dos valores (prioridade absoluta)

                // 1. Verificar 3 linhas anteriores (descrições longas como Windows 365)
                if (indiceLinha >= 3)
                {
                    var linha3Anterior = linhas[indiceLinha - 3].Trim();
                    if (IsValidDescription(linha3Anterior) && !linha3Anterior.Equals("-") && !IsProductCode(linha3Anterior))
                    {
                        return CleanDescription(linha3Anterior);
                    }
                }

                // 2. Verificar 2 linhas anteriores
                if (indiceLinha >= 2)
                {
                    var linha2Anterior = linhas[indiceLinha - 2].Trim();
                    if (IsValidDescription(linha2Anterior) && !linha2Anterior.Equals("-") && !IsProductCode(linha2Anterior))
                    {
                        return CleanDescription(linha2Anterior);
                    }
                }

                // 3. Verificar linha imediatamente anterior
                if (indiceLinha > 0)
                {
                    var linhaAnterior = linhas[indiceLinha - 1].Trim();
                    if (IsValidDescription(linhaAnterior) && !linhaAnterior.Equals("-") && !IsProductCode(linhaAnterior))
                    {
                        return CleanDescription(linhaAnterior);
                    }
                }
            }
            else
            {
                // === LÓGICA PARA PDFs 2024/2025 ===
                // Verificar se há código de produto nas proximidades para determinar direção da busca
                bool temCodigoSeguinte = false;
                for (int k = 1; k <= 3; k++)
                {
                    if (indiceLinha + k < linhas.Length)
                    {
                        var linhaSeguinte = linhas[indiceLinha + k].Trim();
                        if (IsProductCode(linhaSeguinte))
                        {
                            temCodigoSeguinte = true;
                            break;
                        }
                    }
                }

                // Se há código nas linhas seguintes, a descrição provavelmente está depois do código
                if (temCodigoSeguinte)
                {
                    // Procurar descrição APÓS o código
                    for (int k = 1; k <= 4; k++)
                    {
                        if (indiceLinha + k < linhas.Length)
                        {
                            var linhaSeguinte = linhas[indiceLinha + k].Trim();
                            if (IsValidDescription(linhaSeguinte) && !linhaSeguinte.Equals("-") && !IsProductCode(linhaSeguinte))
                            {
                                return CleanDescription(linhaSeguinte);
                            }
                        }
                    }
                }
                else
                {
                    // Procurar descrição ANTES (lógica original)
                    // 1. Verificar 2 linhas anteriores (mais comum)
                    if (indiceLinha >= 2)
                    {
                        var linha2Anterior = linhas[indiceLinha - 2].Trim();
                        if (IsValidDescription(linha2Anterior) && !linha2Anterior.Equals("-") && !IsProductCode(linha2Anterior))
                        {
                            return CleanDescription(linha2Anterior);
                        }
                    }

                    // 2. Verificar linha imediatamente anterior
                    if (indiceLinha > 0)
                    {
                        var linhaAnterior = linhas[indiceLinha - 1].Trim();
                        if (IsValidDescription(linhaAnterior) && !linhaAnterior.Equals("-") && !IsProductCode(linhaAnterior))
                        {
                            return CleanDescription(linhaAnterior);
                        }
                    }

                    // 3. Verificar 3 linhas anteriores (para descrições mais longas)
                    if (indiceLinha >= 3)
                    {
                        var linha3Anterior = linhas[indiceLinha - 3].Trim();
                        if (IsValidDescription(linha3Anterior) && !linha3Anterior.Equals("-") && !IsProductCode(linha3Anterior))
                        {
                            return CleanDescription(linha3Anterior);
                        }
                    }

                    // 4. Verificar 4 linhas anteriores (último recurso para descrições muito longas)
                    if (indiceLinha >= 4)
                    {
                        var linha4Anterior = linhas[indiceLinha - 4].Trim();
                        if (IsValidDescription(linha4Anterior) && !linha4Anterior.Equals("-") && !IsProductCode(linha4Anterior))
                        {
                            return CleanDescription(linha4Anterior);
                        }
                    }
                }
            }



            // === ESTRATÉGIA 3: CSP-AZURE-PLAN (ÚLTIMO RECURSO) ===
            // Só usar se não encontrou nenhuma descrição válida E ainda não foi usado
            // LIMITADO a 2 linhas anteriores para evitar capturar CSP-AZURE-PLAN de outros produtos
            if (!azurePlanJaUsado)
            {
                for (int k = 1; k <= 2; k++)
                {
                    if (indiceLinha >= k)
                    {
                        var linhaAnterior = linhas[indiceLinha - k].Trim();
                        if (linhaAnterior.Equals("CSP-AZURE-PLAN", StringComparison.OrdinalIgnoreCase))
                        {
                            return "Azure Plan";
                        }
                    }
                }
            }









            return "Produto não identificado";
        }



        /// <summary>
        /// Verifica se uma linha é um código de produto (para evitar confusão com descrições)
        /// </summary>
        private bool IsProductCode(string linha)
        {
            if (string.IsNullOrWhiteSpace(linha))
                return false;

            // Códigos específicos conhecidos
            var codigosConhecidos = new[] { "CSP-AZURE-PLAN", "CFQ7TTC0LHSF" };
            if (codigosConhecidos.Contains(linha.Trim(), StringComparer.OrdinalIgnoreCase))
                return true;

            // Códigos numéricos (7 dígitos)
            if (Regex.IsMatch(linha.Trim(), @"^\d{7}$"))
                return true;

            // Códigos alfanuméricos longos
            if (Regex.IsMatch(linha.Trim(), @"^[A-Z0-9\-]{8,}$"))
                return true;

            return false;
        }

        /// <summary>
        /// Verifica se uma linha é uma descrição válida de produto
        /// </summary>
        private bool IsValidDescription(string linha)
        {
            if (string.IsNullOrWhiteSpace(linha) || linha.Length < 3)
                return false;

            // Ignorar linhas que são claramente não-descrições
            var ignorar = new[]
            {
                @"^\d+$", // Apenas números
                @"^To Carry Forward",
                @"^Carried Forward",
                @"^EUR\s*$",
                @"^Page \d+",
                @"^INVOICE$",
                @"^Product/Service/Agreement",
                @"^Quantity Unit Unit Price",
                @"^Item Number Description",
                @"^Agreement number:",
                @"^Publisher:",
                @"^\d+\.?\d*\s+\w+\s+[\d,]+", // Linha de valores
                @"^[A-Z]{2,3}-[A-Z]-[A-Z]{2}$", // Códigos como "CDOM-S-ST"
                @"Processado por programa certificado",
                @"^Amount transported",
                @"^Sales tax code",

                // === CÓDIGOS DE PRODUTO (IGNORAR) ===
                @"^CSP-AZURE-PLAN$", // Código do produto, não descrição do serviço
                @"^CFQ7TTC0LHSF$", // Código do produto Power BI Pro
                @"^[A-Z0-9\-]{8,}$", // Códigos de produto genéricos
                @"^\d{7}$", // Códigos numéricos como "1358250", "1371344"
                @"^1358311$", // Código específico encontrado no PDF 2023

                // === NOMES DE EMPRESAS/ENTIDADES (IGNORAR) ===
                @"^Crayon Software Licensing", // Nome da empresa fornecedora
                @"^BI4ALL Consultores", // Nome da empresa cliente
                @"^Unipessoal LDA", // Tipo de empresa
                @"^CRAYON SOFTWARE LICENSING", // Variação maiúscula

                // === PADRÕES ESPECÍFICOS PARA PDFs 2023 ===
                @"^Total excl\. VAT", // "Total excl. VAT (5 887,76 EUR) 5 887,76 EUR"
                @"^Total VAT", // "Total VAT (1 354,18 EUR) 1 354,18 EUR"
                @"^Invoice Total", // "Invoice Total (7 241,94 EUR) 7 241,94 EUR"
                @"^Supplier Account Number", // "Supplier Account Number Payment Reference Due Date Invoice Total"
                @"^Payment Reference", // Linhas de referência de pagamento
                @"^Due Date",
                @"^\d{2}\.\d{2}\.\d{4}\s+EUR", // "14.10.2023 EUR 7 241,94"
                @"^[\d\s,]+EUR$", // Linhas que terminam apenas com EUR e números
                @"^Bank$",
                @"^SWIFT$",
                @"^IBAN$",
                @"^Tel:$",
                @"^Fax:$",
                @"^Domicile:$",
                @"^Business ID:$",
                @"^VAT-ID:$",
                @"^Additional Info", // Texto de rodapé
                @"^Queries regarding this invoice" // Texto de rodapé
            };

            foreach (var padraoIgnorar in ignorar)
            {
                if (Regex.IsMatch(linha, padraoIgnorar, RegexOptions.IgnoreCase))
                    return false;
            }

            // Deve conter pelo menos uma letra
            if (!Regex.IsMatch(linha, @"[A-Za-z]"))
                return false;

            // === FILTROS ADICIONAIS PARA EVITAR DESCRIÇÕES INCORRETAS ===

            // Rejeitar linhas que são claramente continuações de outras descrições
            if (linha.StartsWith("GB") && linha.Length < 10) // "GB" isolado é continuação de "256 GB"
                return false;

            // Rejeitar linhas que são apenas números ou códigos
            if (Regex.IsMatch(linha, @"^\d+$") && linha.Length < 10) // Números isolados
                return false;

            // Rejeitar linhas muito curtas que não são descrições completas
            if (linha.Length < 5 && !linha.Contains("Pro") && !linha.Contains("E1") && !linha.Contains("E3"))
                return false;

            return true;
        }

        /// <summary>
        /// Normaliza valores decimais lidando com vírgulas como separadores de milhares e decimais
        /// Suporta formatos 2023 com espaços: "5 887,76" e formatos atuais: "5,887.76"
        /// </summary>
        private string NormalizeDecimalValue(string value)
        {
            if (string.IsNullOrWhiteSpace(value))
                return "0";

            // Remover espaços (importante para formatos 2023: "5 887,76" -> "5887,76")
            value = value.Replace(" ", "").Trim();

            // Se contém vírgula e ponto, assumir que vírgula é separador de milhares
            if (value.Contains(",") && value.Contains("."))
            {
                // Formato: 1,234.56 -> remover vírgulas
                return value.Replace(",", "");
            }

            // Se contém apenas vírgula, pode ser separador decimal (formato europeu)
            if (value.Contains(",") && !value.Contains("."))
            {
                // Verificar se é separador de milhares ou decimal
                var lastCommaIndex = value.LastIndexOf(',');
                var afterComma = value.Substring(lastCommaIndex + 1);

                // Se após a vírgula há exatamente 2 dígitos, é separador decimal
                if (afterComma.Length == 2 && afterComma.All(char.IsDigit))
                {
                    return value.Replace(",", ".");
                }
                // Senão, é separador de milhares
                else
                {
                    return value.Replace(",", "");
                }
            }

            // Se contém apenas ponto, manter como está
            return value;
        }

        /// <summary>
        /// Limpa a descrição removendo texto desnecessário
        /// </summary>
        private string CleanDescription(string descricao)
        {
            if (string.IsNullOrWhiteSpace(descricao))
                return "Produto não identificado";

            // === NORMALIZAR AZURE PLAN PRIMEIRO (ANTES DA LIMPEZA) ===
            // Para PDFs 2023: "Azure plan Consumption Period: 2023-08-01" -> "Azure Plan"
            if (descricao.StartsWith("Azure plan", StringComparison.OrdinalIgnoreCase))
            {
                return "Azure Plan";
            }

            if (descricao.Contains("Microsoft CSP Azure Plan", StringComparison.OrdinalIgnoreCase))
            {
                return "Azure Plan";
            }

            // Remover texto após padrões específicos
            var padroesPararEm = new[]
            {
                @"\s+To Carry Forward.*",
                @"\s+Processado por Programa Certificado.*",
                @"\s+Agreement number:.*",
                @"\s+Publisher:.*",
                @"\s+\|\s+Os bens.*"
            };

            foreach (var padrao in padroesPararEm)
            {
                descricao = Regex.Replace(descricao, padrao, "", RegexOptions.IgnoreCase);
            }

            // === REMOVER PERÍODOS DAS DESCRIÇÕES ===
            // Remover "Consumption Period: YYYY-MM-DD - YYYY-MM-DD" e variações
            var padroesPeriodo = new[]
            {
                // Padrões completos com período
                @"Consumption\s+Period:\s*\d{4}-\d{2}-\d{2}\s*-\s*\d{4}-\d{2}-\d{2}",
                @"Period:\s*\d{4}-\d{2}-\d{2}\s*-\s*\d{4}-\d{2}-\d{2}",
                @"Start\s+Date:\s*\d{4}-\d{2}-\d{2}.*?End\s+date:\s*\d{4}-\d{2}-\d{2}",

                // === PADRÕES ESPECÍFICOS PARA PDFs 2023 ===
                // "Azure plan Consumption Period: 2023-08-01" (sem data fim)
                @"Azure\s+plan\s+Consumption\s+Period:\s*\d{4}-\d{2}-\d{2}",
                @"Consumption\s+Period:\s*\d{4}-\d{2}-\d{2}(?!\s*-)", // Sem traço após a data

                // Padrões genéricos
                @"\d{4}-\d{2}-\d{2}\s*-\s*\d{4}-\d{2}-\d{2}",
                @"\d{4}-\d{2}-\d{2}(?=\s*$)" // Data isolada no final da linha
            };

            foreach (var padrao in padroesPeriodo)
            {
                descricao = Regex.Replace(descricao, padrao, "", RegexOptions.IgnoreCase).Trim();
            }

            // === REMOVER CÓDIGOS DE PRODUTO DAS DESCRIÇÕES ===
            // Remover códigos como "CSP-AZURE-PLAN - " e "CFQ7TTC0LHSF - "
            var padroesCodigos = new[]
            {
                @"^CSP-AZURE-PLAN\s*-\s*", // "CSP-AZURE-PLAN - Microsoft CSP Azure Plan" -> "Microsoft CSP Azure Plan"
                @"^CFQ7TTC0LHSF\s*-\s*", // "CFQ7TTC0LHSF - Power BI Pro" -> "Power BI Pro"
                @"^[A-Z0-9\-]{8,}\s*-\s*", // Códigos genéricos seguidos de traço
                @"^\d{7}\s*-\s*" // Códigos numéricos seguidos de traço
            };

            foreach (var padrao in padroesCodigos)
            {
                descricao = Regex.Replace(descricao, padrao, "", RegexOptions.IgnoreCase).Trim();
            }

            // === NORMALIZAR OUTROS NOMES DE SERVIÇOS ===
            // Normalizar apenas "Azure plan" exato (outros casos já tratados acima)
            if (descricao.Trim().Equals("Azure plan", StringComparison.OrdinalIgnoreCase))
            {
                descricao = "Azure Plan";
            }

            // Remover traços e espaços extras no final
            descricao = descricao.Trim(' ', '-', ':').Trim();

            return descricao.Trim();
        }

        /// <summary>
        /// Debug de períodos extraídos de um PDF específico - SIMPLIFICADO
        /// </summary>
        public async Task<object> DebugPeriodsAsync(string fileName)
        {
            return new { Message = "Debug temporariamente desativado para focar na solução principal" };
        }
    }
}
