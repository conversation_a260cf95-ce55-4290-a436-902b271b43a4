{"openapi": "3.0.1", "info": {"title": "R&D Backend Framework", "version": "v1"}, "paths": {"/api/auth/me": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/User"}}, "application/json": {"schema": {"$ref": "#/components/schemas/User"}}, "text/json": {"schema": {"$ref": "#/components/schemas/User"}}}}}}}, "/api/auth/permissions": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "Success"}}}}, "/api/auth/register": {"post": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "Success"}}}}, "/api/auth/azure-users-debug": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "Success"}}}}, "/api/auth/azure-users": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {}}}, "application/json": {"schema": {"type": "array", "items": {}}}, "text/json": {"schema": {"type": "array", "items": {}}}}}}}}, "/api/auth/azure-users-public": {"get": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "searchText", "in": "query", "schema": {"type": "string"}}, {"name": "email", "in": "query", "schema": {"type": "string"}}, {"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 0}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 100}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {}}}, "application/json": {"schema": {"type": "array", "items": {}}}, "text/json": {"schema": {"type": "array", "items": {}}}}}}}}, "/api/auth/azure-users-delegated": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {}}}, "application/json": {"schema": {"type": "array", "items": {}}}, "text/json": {"schema": {"type": "array", "items": {}}}}}}}}, "/api/auth-debug/test-connection": {"get": {"tags": ["AuthDebug"], "responses": {"200": {"description": "Success"}}}}, "/api/auth-debug/register-test-user": {"post": {"tags": ["AuthDebug"], "responses": {"200": {"description": "Success"}}}}, "/api/auth-debug/users": {"get": {"tags": ["AuthDebug"], "responses": {"200": {"description": "Success"}}}}, "/api/auth-debug/claims": {"get": {"tags": ["AuthDebug"], "responses": {"200": {"description": "Success"}}}}, "/api/auth-debug/create-direct-user": {"get": {"tags": ["AuthDebug"], "responses": {"200": {"description": "Success"}}}}, "/api/auth-debug/check-table-structure": {"get": {"tags": ["AuthDebug"], "responses": {"200": {"description": "Success"}}}}, "/api/auth-debug/create-table": {"post": {"tags": ["AuthDebug"], "responses": {"200": {"description": "Success"}}}}, "/api/auth-debug/generate-azure-user": {"get": {"tags": ["AuthDebug"], "responses": {"200": {"description": "Success"}}}}, "/api/Cliente": {"get": {"tags": ["Cliente"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Cliente"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Cliente"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Cliente"}}}}}}}, "post": {"tags": ["Cliente"], "parameters": [{"name": "ID", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Name", "in": "query", "schema": {"type": "string"}}, {"name": "Contact", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "Consumo_Cliente", "in": "query", "schema": {"type": "array", "items": {"$ref": "#/components/schemas/Consumo_Cliente"}}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cliente"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cliente"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cliente"}}}}}}}, "/api/Cliente/{id}": {"get": {"tags": ["Cliente"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cliente"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cliente"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cliente"}}}}}}, "put": {"tags": ["Cliente"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "ID", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "Name", "in": "query", "schema": {"type": "string"}}, {"name": "Contact", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "Consumo_Cliente", "in": "query", "schema": {"type": "array", "items": {"$ref": "#/components/schemas/Consumo_Cliente"}}}], "responses": {"200": {"description": "Success"}}}, "delete": {"tags": ["Cliente"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/ClienteSubscricao": {"post": {"tags": ["ClienteSubscricao"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClienteSubscricaoInputModel"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ClienteSubscricao"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ClienteSubscricao"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ClienteSubscricao"}}}}}}}, "/api/ClienteSubscricao/cliente/{clienteId}": {"get": {"tags": ["ClienteSubscricao"], "parameters": [{"name": "clienteId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, "application/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}}}}}}, "/api/ClienteSubscricao/cliente/{clienteId}/details": {"get": {"tags": ["ClienteSubscricao"], "parameters": [{"name": "clienteId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {}}}, "application/json": {"schema": {"type": "array", "items": {}}}, "text/json": {"schema": {"type": "array", "items": {}}}}}}}}, "/api/ClienteSubscricao/available": {"get": {"tags": ["ClienteSubscricao"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {}}}, "application/json": {"schema": {"type": "array", "items": {}}}, "text/json": {"schema": {"type": "array", "items": {}}}}}}}}, "/api/ClienteSubscricao/cliente/{clienteId}/subscricao/{subscriptionId}": {"delete": {"tags": ["ClienteSubscricao"], "parameters": [{"name": "clienteId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "subscriptionId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Consumo": {"get": {"tags": ["Consu<PERSON>"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Consumo"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Consumo"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Consumo"}}}}}}}, "post": {"tags": ["Consu<PERSON>"], "parameters": [{"name": "ID", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "ClienteID", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "DataInicio", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "DataFim", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "CountryListTotal", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "CountryResellerTotal", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "CountryCustomerTotal", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "Regiao", "in": "query", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "Consumo_Cliente", "in": "query", "schema": {"type": "array", "items": {"$ref": "#/components/schemas/Consumo_Cliente"}}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Consumo"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Consumo"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Consumo"}}}}}}}, "/api/Consumo/{id}": {"get": {"tags": ["Consu<PERSON>"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Consumo"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Consumo"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Consumo"}}}}}}}, "/api/Consumo/FromExcel": {"get": {"tags": ["Consu<PERSON>"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Consumo"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Consumo"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Consumo"}}}}}}}}, "/api/Consumo/upload": {"post": {"tags": ["Consu<PERSON>"], "summary": "Faz upload de um arquivo Excel para processamento no container Reseller", "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {}}, "application/json": {"schema": {}}, "text/json": {"schema": {}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}}}}, "/api/Consumo_Cliente": {"get": {"tags": ["Consumo_Cliente"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Consumo_Cliente"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Consumo_Cliente"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Consumo_Cliente"}}}}}}}, "post": {"tags": ["Consumo_Cliente"], "parameters": [{"name": "ID", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "ClienteID", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "Cliente.ID", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Cliente.Name", "in": "query", "schema": {"type": "string"}}, {"name": "Cliente.Contact", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "Cliente.Consumo_Cliente", "in": "query", "schema": {"type": "array", "items": {"$ref": "#/components/schemas/Consumo_Cliente"}}}, {"name": "ConsumoID", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "Consumo.ID", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Consumo.ClienteID", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Consumo.DataInicio", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Consumo.DataFim", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Consumo.CountryListTotal", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "Consumo.CountryResellerTotal", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "Consumo.CountryCustomerTotal", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "Consumo.TipoServico", "in": "query", "schema": {"type": "string"}}, {"name": "Consumo.Regiao", "in": "query", "schema": {"type": "string"}}, {"name": "Consumo.Moeda", "in": "query", "schema": {"type": "string"}}, {"name": "Consumo.Consumo_Cliente", "in": "query", "schema": {"type": "array", "items": {"$ref": "#/components/schemas/Consumo_Cliente"}}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Consumo_Cliente"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Consumo_Cliente"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Consumo_Cliente"}}}}}}}, "/api/Consumo_Cliente/cliente/{clienteId}": {"get": {"tags": ["Consumo_Cliente"], "parameters": [{"name": "clienteId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Consumo_Cliente"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Consumo_Cliente"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Consumo_Cliente"}}}}}}}}, "/api/ConsumoExcelAzure/upload": {"post": {"tags": ["ConsumoExcelAzure"], "summary": "Faz upload de um arquivo Excel para processamento no container Azure", "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {}}, "application/json": {"schema": {}}, "text/json": {"schema": {}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}}}}, "/api/ConsumoPDF/upload": {"post": {"tags": ["ConsumoPDF"], "summary": "Faz upload de um arquivo PDF para processamento", "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {}}, "application/json": {"schema": {}}, "text/json": {"schema": {}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}}}}, "/api/ConsumoPDF/processar/{fileName}": {"post": {"tags": ["ConsumoPDF"], "summary": "Processa um arquivo PDF já existente no Azure Storage", "parameters": [{"name": "fileName", "in": "path", "description": "Nome do arquivo PDF no Azure Storage", "required": true, "schema": {"type": "string"}}, {"name": "forceReprocessing", "in": "query", "description": "Se deve forçar o reprocessamento mesmo se o arquivo já foi processado", "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {}}, "application/json": {"schema": {}}, "text/json": {"schema": {}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}}}}, "/api/ConsumoPDF": {"get": {"tags": ["ConsumoPDF"], "summary": "Obtém todos os registros de ConsumoPDF", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ConsumoPDF"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ConsumoPDF"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ConsumoPDF"}}}}}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}}}}, "/api/ConsumoPDF/cliente/{clienteId}": {"get": {"tags": ["ConsumoPDF"], "summary": "Obtém os registros de ConsumoPDF de um cliente específico", "parameters": [{"name": "clienteId", "in": "path", "description": "ID do cliente", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ConsumoPDF"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ConsumoPDF"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ConsumoPDF"}}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}}}}, "/api/ConsumoPDF/crayon-raw-text": {"get": {"tags": ["ConsumoPDF"], "summary": "Obtém o texto bruto de todos os PDFs Crayon no container Azure", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {}}, "application/json": {"schema": {}}, "text/json": {"schema": {}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}}}}, "/api/ConsumoPDF/crayon-extracted-data": {"get": {"tags": ["ConsumoPDF"], "summary": "Obtém os dados extraídos de todos os PDFs Crayon no container Azure - VERSÃO DE TESTE (SEM PERSISTIR)", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {}}, "application/json": {"schema": {}}, "text/json": {"schema": {}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}}}}, "/api/ConsumoPDF/test-pdf-2023/{fileName}": {"get": {"tags": ["ConsumoPDF"], "summary": "Testa extração de um PDF específico de 2023 para debug", "parameters": [{"name": "fileName", "in": "path", "description": "Nome do arquivo PDF", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {}}, "application/json": {"schema": {}}, "text/json": {"schema": {}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}}}}, "/api/ConsumoPDF/test-pdf/{fileName}": {"get": {"tags": ["ConsumoPDF"], "summary": "Testa extração de qualquer PDF para debug", "parameters": [{"name": "fileName", "in": "path", "description": "Nome do arquivo PDF", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {}}, "application/json": {"schema": {}}, "text/json": {"schema": {}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}}}}, "/api/ConsumoPDF/test-all-pdfs": {"get": {"tags": ["ConsumoPDF"], "summary": "Testa extração de TODOS os PDFs disponíveis no container", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {}}, "application/json": {"schema": {}}, "text/json": {"schema": {}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}}}}, "/api/ConsumoPDF/debug-periods/{fileName}": {"get": {"tags": ["ConsumoPDF"], "summary": "Debug de períodos extraídos de um PDF específico", "parameters": [{"name": "fileName", "in": "path", "description": "Nome do arquivo PDF", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {}}, "application/json": {"schema": {}}, "text/json": {"schema": {}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}}}}, "/api/ConsumoSubscricao": {"get": {"tags": ["ConsumoSubscricao"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ConsumoSubscricao"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ConsumoSubscricao"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ConsumoSubscricao"}}}}}}}}, "/api/ConsumoSubscricao/subscription/{subscriptionId}": {"get": {"tags": ["ConsumoSubscricao"], "parameters": [{"name": "subscriptionId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ConsumoSubscricao"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ConsumoSubscricao"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ConsumoSubscricao"}}}}}}}}, "/api/ConsumoSubscricao/period": {"get": {"tags": ["ConsumoSubscricao"], "parameters": [{"name": "startDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "endDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ConsumoSubscricao"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ConsumoSubscricao"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ConsumoSubscricao"}}}}}}}}, "/api/ConsumoSubscricao/summary": {"get": {"tags": ["ConsumoSubscricao"], "responses": {"200": {"description": "Success"}}}}, "/api/ConsumoSubscricao/cliente/{clienteId}": {"get": {"tags": ["ConsumoSubscricao"], "parameters": [{"name": "clienteId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ConsumoSubscricao"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ConsumoSubscricao"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ConsumoSubscricao"}}}}}}}}, "/api/ConsumoSubscricao/cliente/{clienteId}/summary": {"get": {"tags": ["ConsumoSubscricao"], "parameters": [{"name": "clienteId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "startDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "endDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "Success"}}}}, "/api/ConsumoSubscricaoExcel/processar/{fileName}": {"post": {"tags": ["ConsumoSubscricaoExcel"], "parameters": [{"name": "fileName", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ConsumoSubscricaoExcelModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ConsumoSubscricaoExcelModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ConsumoSubscricaoExcelModel"}}}}}}}}, "/api/DataEntry": {"get": {"tags": ["DataEntry"], "summary": "Gets the data entry entities.", "responses": {"200": {"description": "Returns a List of entities", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EntityListDtoApiOutput"}, "example": {"code": 200, "description": null, "value": {"entities": [{"id": "9dcfd5fd-ad84-45f2-a326-2792e6ffad49:2645a7b9-b772-4a3f-a032-83ba24f56dff", "modelId": "9dcfd5fd-ad84-45f2-a326-2792e6ffad49", "modelName": null, "functionalName": "Product Category", "technicalName": "product_category", "dataLength": 4, "created": "2022-11-13T22:30:47.081+00:00", "createdBy": "<EMAIL>", "createdByName": null, "modified": "2022-11-14T15:10:57.481+00:00", "modifiedBy": "<EMAIL>", "modifiedByName": null}, {"id": "a7d4a4c1-d314-475e-82c2-40926f022666:6d2418f6-437f-4913-a343-fe98fe435ef7", "modelId": "a7d4a4c1-d314-475e-82c2-40926f022666", "modelName": null, "functionalName": "Team", "technicalName": "team", "dataLength": 3, "created": "2022-10-25T18:52:16.425+01:00", "createdBy": "<EMAIL>", "createdByName": null, "modified": "2022-11-30T14:50:27.848+00:00", "modifiedBy": "<EMAIL>", "modifiedByName": null}], "totalRows": 2}, "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}}}, "500": {"description": "Internal error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}}}, "204": {"description": "No content response"}}}}, "/api/DynamicEntity": {"post": {"tags": ["DynamicEntity"], "summary": "Adds the entity in the request.", "requestBody": {"description": "The request.", "content": {"application/json-patch+json": {"schema": {"type": "object", "additionalProperties": {}}, "examples": {"Company": {"value": {"entityId": "dive1", "companyId": "diveCenterPhi2", "name": "Dive Center Koh Phi Phi", "type": "Dive Center", "review": 4.5, "numberOfReviews": 104, "location": "Koh <PERSON> Island", "country": "Thailand"}}, "Car": {"value": {"entityId": "car1", "brand": "BMW", "brandId": "bmw", "modelName": "Serie 3", "fuelType": "Diesel", "version": "M", "year": 2022}}, "Person": {"value": {"entityId": "person1", "name": "<PERSON><PERSON>", "profession": "Software Engineer", "professionId": "softwareEngineer", "Role": "Trainee", "nationality": "Portuguese", "gender": "M", "year": 2022}}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {}}, "examples": {"Company": {"value": {"entityId": "dive1", "companyId": "diveCenterPhi2", "name": "Dive Center Koh Phi Phi", "type": "Dive Center", "review": 4.5, "numberOfReviews": 104, "location": "Koh <PERSON> Island", "country": "Thailand"}}, "Car": {"value": {"entityId": "car1", "brand": "BMW", "brandId": "bmw", "modelName": "Serie 3", "fuelType": "Diesel", "version": "M", "year": 2022}}, "Person": {"value": {"entityId": "person1", "name": "<PERSON><PERSON>", "profession": "Software Engineer", "professionId": "softwareEngineer", "Role": "Trainee", "nationality": "Portuguese", "gender": "M", "year": 2022}}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {}}, "examples": {"Company": {"value": {"entityId": "dive1", "companyId": "diveCenterPhi2", "name": "Dive Center Koh Phi Phi", "type": "Dive Center", "review": 4.5, "numberOfReviews": 104, "location": "Koh <PERSON> Island", "country": "Thailand"}}, "Car": {"value": {"entityId": "car1", "brand": "BMW", "brandId": "bmw", "modelName": "Serie 3", "fuelType": "Diesel", "version": "M", "year": 2022}}, "Person": {"value": {"entityId": "person1", "name": "<PERSON><PERSON>", "profession": "Software Engineer", "professionId": "softwareEngineer", "Role": "Trainee", "nationality": "Portuguese", "gender": "M", "year": 2022}}}}, "application/*+json": {"schema": {"type": "object", "additionalProperties": {}}, "examples": {"Company": {"value": {"entityId": "dive1", "companyId": "diveCenterPhi2", "name": "Dive Center Koh Phi Phi", "type": "Dive Center", "review": 4.5, "numberOfReviews": 104, "location": "Koh <PERSON> Island", "country": "Thailand"}}, "Car": {"value": {"entityId": "car1", "brand": "BMW", "brandId": "bmw", "modelName": "Serie 3", "fuelType": "Diesel", "version": "M", "year": 2022}}, "Person": {"value": {"entityId": "person1", "name": "<PERSON><PERSON>", "profession": "Software Engineer", "professionId": "softwareEngineer", "Role": "Trainee", "nationality": "Portuguese", "gender": "M", "year": 2022}}}}}}, "responses": {"200": {"description": "Returns The added entity.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StringObjectDictionaryApiOutput"}, "example": {"code": 200, "description": null, "value": {"entityId": "dive1", "companyId": "diveCenterPhi2", "name": "Dive Center Koh Phi Phi", "type": "Dive Center", "review": 4.5, "numberOfReviews": 104, "location": "Koh <PERSON> Island", "country": "Thailand", "id": "dive1:9e21138f-21d6-4c58-a518-2d0cb896b395", "_rid": "DMV6ANEfcywFAAAAAAAAAA==", "_self": "dbs/DMV6AA==/colls/DMV6ANEfcyw=/docs/DMV6ANEfcywFAAAAAAAAAA==/", "_etag": "\"5100a39d-0000-0700-0000-63f8fa130000\"", "_attachments": "attachments/", "_ts": 1677261331}, "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}}}, "400": {"description": "Malformed request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}}}, "500": {"description": "Internal error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}}}}}, "put": {"tags": ["DynamicEntity"], "summary": "Updates the entity in the request.", "parameters": [{"name": "etag", "in": "query", "description": "The etag.", "schema": {"type": "string"}}], "requestBody": {"description": "The request.", "content": {"application/json-patch+json": {"schema": {"type": "object", "additionalProperties": {}}, "example": {"entityId": "dive1", "companyId": "diveCenterPhi1", "name": "Dive Center Koh Phi Phi", "type": "Dive Center", "review": 4.5, "numberOfReviews": 104, "location": "Koh <PERSON> Island", "country": "Thailand", "id": "dive1:bfd6b932-9e6a-43f2-b739-0b65213bc333"}}, "application/json": {"schema": {"type": "object", "additionalProperties": {}}, "example": {"entityId": "dive1", "companyId": "diveCenterPhi1", "name": "Dive Center Koh Phi Phi", "type": "Dive Center", "review": 4.5, "numberOfReviews": 104, "location": "Koh <PERSON> Island", "country": "Thailand", "id": "dive1:bfd6b932-9e6a-43f2-b739-0b65213bc333"}}, "text/json": {"schema": {"type": "object", "additionalProperties": {}}, "example": {"entityId": "dive1", "companyId": "diveCenterPhi1", "name": "Dive Center Koh Phi Phi", "type": "Dive Center", "review": 4.5, "numberOfReviews": 104, "location": "Koh <PERSON> Island", "country": "Thailand", "id": "dive1:bfd6b932-9e6a-43f2-b739-0b65213bc333"}}, "application/*+json": {"schema": {"type": "object", "additionalProperties": {}}, "example": {"entityId": "dive1", "companyId": "diveCenterPhi1", "name": "Dive Center Koh Phi Phi", "type": "Dive Center", "review": 4.5, "numberOfReviews": 104, "location": "Koh <PERSON> Island", "country": "Thailand", "id": "dive1:bfd6b932-9e6a-43f2-b739-0b65213bc333"}}}}, "responses": {"200": {"description": "Returns The updated entity.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StringObjectDictionaryApiOutput"}, "example": {"code": 200, "description": null, "value": {"entityId": "dive1", "companyId": "diveCenterPhi1", "name": "Dive Center Koh Phi Phi", "type": "Dive Center", "review": 4.5, "numberOfReviews": 104, "location": "Koh <PERSON> Island", "country": "Thailand", "id": "dive1:bfd6b932-9e6a-43f2-b739-0b65213bc333", "_rid": "DMV6ANEfcywBAAAAAAAAAA==", "_self": "dbs/DMV6AA==/colls/DMV6ANEfcyw=/docs/DMV6ANEfcywBAAAAAAAAAA==/", "_etag": "\"510015a5-0000-0700-0000-63f8fbe10000\"", "_attachments": "attachments/", "_ts": 1677261793}, "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}}}, "400": {"description": "Malformed request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}}}, "412": {"description": "Error updating entity.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Error updating entity.", "value": null, "error": true, "exceptionMessages": {"messages": ["PreconditionFailed - Error updating entity."], "hasMessages": true}, "properties": {}}}}}, "500": {"description": "Internal error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}}}}}, "get": {"tags": ["DynamicEntity"], "summary": "Lists the entities.", "parameters": [{"name": "pageNumber", "in": "query", "description": "The page number.", "schema": {"type": "integer", "format": "int32"}}, {"name": "maxItems", "in": "query", "description": "The maximum items.", "schema": {"type": "integer", "format": "int32"}}, {"name": "sortField", "in": "query", "description": "The sort field.", "schema": {"type": "string"}}, {"name": "sortAscending", "in": "query", "description": "if set to `true` [sort ascending].", "schema": {"type": "boolean"}}, {"name": "continuationToken", "in": "query", "description": "The continuation token.", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns The list of entities.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StringObjectDictionaryListApiOutput"}, "example": {"code": 200, "description": null, "value": [{"entityId": "dive1", "companyId": "diveCenterPhi1", "name": "Dive Center Koh Phi Phi", "type": "Dive Center", "review": 4.5, "numberOfReviews": 104, "location": "Koh <PERSON> Island", "country": "Thailand", "id": "dive1:bfd6b932-9e6a-43f2-b739-0b65213bc333", "_rid": "DMV6ANEfcywBAAAAAAAAAA==", "_self": "dbs/DMV6AA==/colls/DMV6ANEfcyw=/docs/DMV6ANEfcywBAAAAAAAAAA==/", "_etag": "\"510015a5-0000-0700-0000-63f8fbe10000\"", "_attachments": "attachments/", "_ts": 1677261793}, {"entityId": "car1", "brand": "BMW", "brandId": "bmw", "modelName": "Serie 3", "fuelType": "Diesel", "version": "M", "year": 2022, "id": "car1:3446381a-27fd-45d9-98ba-7f4c6d155316", "_rid": "DMV6ANEfcywCAAAAAAAAAA==", "_self": "dbs/DMV6AA==/colls/DMV6ANEfcyw=/docs/DMV6ANEfcywCAAAAAAAAAA==/", "_etag": "\"0000f160-0000-0700-0000-63daa6200000\"", "_attachments": "attachments/", "_ts": 1675273760}], "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {"continuation_token": "W3sidG9rZW4iOiItUklEOn5ETVY2QU5FZmN5d0ZBQUFBQUFBQUFBPT0jUlQ6MiNUUkM6MyNJU1Y6MiNJRU86NjU1NjcjUUNGOjgiLCJyYW5nZSI6eyJtaW4iOiIiLCJtYXgiOiJGRiJ9fV0"}}}}}, "400": {"description": "Malformed request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}}}, "500": {"description": "Internal error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}}}, "204": {"description": "No content response"}}}}, "/api/DynamicEntity/{id}": {"delete": {"tags": ["DynamicEntity"], "summary": "Deletes an entity by identifier.", "parameters": [{"name": "id", "in": "path", "description": "The identifier.", "required": true, "schema": {"type": "string"}}], "responses": {"400": {"description": "Malformed request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}}}, "500": {"description": "Internal error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}}}, "204": {"description": "No content response"}}}, "get": {"tags": ["DynamicEntity"], "summary": "Gets an entity by identifier.", "parameters": [{"name": "id", "in": "path", "description": "The identifier.", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns The entity.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StringObjectDictionaryApiOutput"}, "example": {"code": 200, "description": null, "value": {"entityId": "dive1", "companyId": "diveCenterPhi1", "name": "Dive Center Koh Phi Phi", "type": "Dive Center", "review": 4.5, "numberOfReviews": 104, "location": "Koh <PERSON> Island", "country": "Thailand", "id": "dive1:bfd6b932-9e6a-43f2-b739-0b65213bc333", "_rid": "DMV6ANEfcywBAAAAAAAAAA==", "_self": "dbs/DMV6AA==/colls/DMV6ANEfcyw=/docs/DMV6ANEfcywBAAAAAAAAAA==/", "_etag": "\"510015a5-0000-0700-0000-63f8fbe10000\"", "_attachments": "attachments/", "_ts": 1677261793}, "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}}}, "400": {"description": "Malformed request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}}}, "500": {"description": "Internal error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}}}, "204": {"description": "No content response"}}}}, "/api/Email/SendCodeAcademyEmail": {"post": {"tags": ["Email"], "summary": "Sends the code academy email.", "requestBody": {"description": "The request.", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/CodeAcademyTemplateDtoEmailMessageDto"}, "example": {"templateData": {"session": "SendGrid email service", "date": "19-07-2022", "message": "This is a test e-mail", "subject": "Test e-mail"}, "recipients": ["<EMAIL>"], "recipientsCC": null, "recipientsBCC": null}}, "application/json": {"schema": {"$ref": "#/components/schemas/CodeAcademyTemplateDtoEmailMessageDto"}, "example": {"templateData": {"session": "SendGrid email service", "date": "19-07-2022", "message": "This is a test e-mail", "subject": "Test e-mail"}, "recipients": ["<EMAIL>"], "recipientsCC": null, "recipientsBCC": null}}, "text/json": {"schema": {"$ref": "#/components/schemas/CodeAcademyTemplateDtoEmailMessageDto"}, "example": {"templateData": {"session": "SendGrid email service", "date": "19-07-2022", "message": "This is a test e-mail", "subject": "Test e-mail"}, "recipients": ["<EMAIL>"], "recipientsCC": null, "recipientsBCC": null}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CodeAcademyTemplateDtoEmailMessageDto"}, "example": {"templateData": {"session": "SendGrid email service", "date": "19-07-2022", "message": "This is a test e-mail", "subject": "Test e-mail"}, "recipients": ["<EMAIL>"], "recipientsCC": null, "recipientsBCC": null}}}}, "responses": {"200": {"description": "Returns true if e-mail was sent, otherwise False", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BooleanApiOutput"}, "example": {"code": 200, "description": null, "value": true, "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}}}, "400": {"description": "Malformed request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}}}, "500": {"description": "Internal error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}}}}}}, "/api/Email/SendDefaultTemplateEmail": {"post": {"tags": ["Email"], "summary": "Sends the email with the default template.", "requestBody": {"description": "The request.", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/EmailDtoEmailMessageDto"}, "example": {"templateData": {"message": "This is a test e-mail", "subject": "Test e-mail"}, "recipients": ["<EMAIL>"], "recipientsCC": ["<EMAIL>"], "recipientsBCC": ["<EMAIL>"]}}, "application/json": {"schema": {"$ref": "#/components/schemas/EmailDtoEmailMessageDto"}, "example": {"templateData": {"message": "This is a test e-mail", "subject": "Test e-mail"}, "recipients": ["<EMAIL>"], "recipientsCC": ["<EMAIL>"], "recipientsBCC": ["<EMAIL>"]}}, "text/json": {"schema": {"$ref": "#/components/schemas/EmailDtoEmailMessageDto"}, "example": {"templateData": {"message": "This is a test e-mail", "subject": "Test e-mail"}, "recipients": ["<EMAIL>"], "recipientsCC": ["<EMAIL>"], "recipientsBCC": ["<EMAIL>"]}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/EmailDtoEmailMessageDto"}, "example": {"templateData": {"message": "This is a test e-mail", "subject": "Test e-mail"}, "recipients": ["<EMAIL>"], "recipientsCC": ["<EMAIL>"], "recipientsBCC": ["<EMAIL>"]}}}}, "responses": {"200": {"description": "Returns true if e-mail was sent, otherwise False", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BooleanApiOutput"}, "example": {"code": 200, "description": null, "value": true, "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}}}, "400": {"description": "Malformed request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}}}, "500": {"description": "Internal error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}}}}}}, "/api/Email/SendTemplatedEmail": {"post": {"tags": ["Email"], "summary": "Sends the email with a specific template Id.", "requestBody": {"description": "The request.", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/SpecificTemplateDto"}, "example": {"templateId": "d-8d3eac75edc84f6e87cdfe48a3e850b5", "templateData": {"session": "SendGrid email service", "date": "19-07-2022", "message": "This is a test e-mail", "subject": "Code Academy Email Service"}, "recipients": ["<EMAIL>"], "recipientsCC": null, "recipientsBCC": null}}, "application/json": {"schema": {"$ref": "#/components/schemas/SpecificTemplateDto"}, "example": {"templateId": "d-8d3eac75edc84f6e87cdfe48a3e850b5", "templateData": {"session": "SendGrid email service", "date": "19-07-2022", "message": "This is a test e-mail", "subject": "Code Academy Email Service"}, "recipients": ["<EMAIL>"], "recipientsCC": null, "recipientsBCC": null}}, "text/json": {"schema": {"$ref": "#/components/schemas/SpecificTemplateDto"}, "example": {"templateId": "d-8d3eac75edc84f6e87cdfe48a3e850b5", "templateData": {"session": "SendGrid email service", "date": "19-07-2022", "message": "This is a test e-mail", "subject": "Code Academy Email Service"}, "recipients": ["<EMAIL>"], "recipientsCC": null, "recipientsBCC": null}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SpecificTemplateDto"}, "example": {"templateId": "d-8d3eac75edc84f6e87cdfe48a3e850b5", "templateData": {"session": "SendGrid email service", "date": "19-07-2022", "message": "This is a test e-mail", "subject": "Code Academy Email Service"}, "recipients": ["<EMAIL>"], "recipientsCC": null, "recipientsBCC": null}}}}, "responses": {"200": {"description": "Returns true if e-mail was sent, otherwise False", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BooleanApiOutput"}, "example": {"code": 200, "description": null, "value": true, "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}}}, "400": {"description": "Malformed request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}}}, "500": {"description": "Internal error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}}}}}}, "/api/Email/SendDefaultTemplateEmailWithAttachments": {"post": {"tags": ["Email"], "summary": "Sends the default template email with attachments.", "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"Attachments": {"type": "array", "items": {"type": "string", "format": "binary"}, "description": "Gets or sets the attachments."}, "TemplateData.Message": {"type": "string", "description": "Gets or sets the message."}, "TemplateData.Subject": {"type": "string", "description": "Gets or sets the subject."}, "Recipients": {"type": "array", "items": {"type": "string"}, "description": "Gets or sets the recipients."}, "RecipientsCC": {"type": "array", "items": {"type": "string"}, "description": "Gets or sets the recipients cc."}, "RecipientsBCC": {"type": "array", "items": {"type": "string"}, "description": "Gets or sets the recipients BCC."}}}, "example": {"attachments": [], "templateData": {"message": "The attachments email message.", "subject": "An e-mail with attachments"}, "recipients": ["<EMAIL>"], "recipientsCC": null, "recipientsBCC": null}, "encoding": {"Attachments": {"style": "form"}, "TemplateData.Message": {"style": "form"}, "TemplateData.Subject": {"style": "form"}, "Recipients": {"style": "form"}, "RecipientsCC": {"style": "form"}, "RecipientsBCC": {"style": "form"}}}}}, "responses": {"200": {"description": "Returns true if e-mail was sent, otherwise False", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BooleanApiOutput"}, "example": {"code": 200, "description": null, "value": true, "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}}}, "400": {"description": "Malformed request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}}}, "500": {"description": "Internal error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}}}}}}, "/api/LogProcessamento": {"get": {"tags": ["LogProcessamento"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/LogProcessamento"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/LogProcessamento"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/LogProcessamento"}}}}}}}}, "/api/LogProcessamento/arquivo/{fileId}": {"get": {"tags": ["LogProcessamento"], "parameters": [{"name": "fileId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/LogProcessamento"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/LogProcessamento"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/LogProcessamento"}}}}}}}}, "/api/LogProcessamento/tipo/{tipo}": {"get": {"tags": ["LogProcessamento"], "parameters": [{"name": "tipo", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/LogProcessamento"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/LogProcessamento"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/LogProcessamento"}}}}}}}}, "/api/LogProcessamento/arquivos": {"get": {"tags": ["LogProcessamento"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ArquivoProcessado"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ArquivoProcessado"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ArquivoProcessado"}}}}}}}}, "/api/LogProcessamento/arquivos/{id}": {"get": {"tags": ["LogProcessamento"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ArquivoProcessado"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ArquivoProcessado"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ArquivoProcessado"}}}}}}}, "/api/permissions": {"get": {"tags": ["Permissions"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Permission"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Permission"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Permission"}}}}}}}, "post": {"tags": ["Permissions"], "parameters": [{"name": "Id", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Name", "in": "query", "required": true, "schema": {"maxLength": 126, "type": "string"}}, {"name": "UserPermissions", "in": "query", "schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserPermission"}}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Permission"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Permission"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Permission"}}}}}}}, "/api/Subscription/available": {"get": {"tags": ["Subscription"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Subscription"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Subscription"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Subscription"}}}}}}}}, "/api/Subscription": {"get": {"tags": ["Subscription"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Subscription"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Subscription"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Subscription"}}}}}}}}, "/api/user/me": {"get": {"tags": ["User"], "responses": {"200": {"description": "Success"}}}}, "/api/user/GetUserDetails": {"get": {"tags": ["User"], "parameters": [{"name": "username", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/user/register": {"post": {"tags": ["User"], "responses": {"200": {"description": "Success"}}}}, "/api/user/force-register": {"post": {"tags": ["User"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/UserRegisterRequest"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserRegisterRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserRegisterRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserRegisterRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/user/fallback-register": {"post": {"tags": ["User"], "responses": {"200": {"description": "Success"}}}}, "/api/user/auth-info": {"get": {"tags": ["User"], "responses": {"200": {"description": "Success"}}}}, "/api/user/add-azure-users": {"post": {"tags": ["User"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/AddAzureUsersRequest"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AddAzureUsersRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AddAzureUsersRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AddAzureUsersRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/user/add-azure-users-test": {"post": {"tags": ["User"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/AddAzureUsersRequest"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AddAzureUsersRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AddAzureUsersRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AddAzureUsersRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/user/add-azure-users-public": {"post": {"tags": ["User"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/AddAzureUsersRequest"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AddAzureUsersRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AddAzureUsersRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AddAzureUsersRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/user-permissions/{userId}": {"get": {"tags": ["UserPermissions"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/user-permissions": {"post": {"tags": ["UserPermissions"], "parameters": [{"name": "userId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "permissionId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}, "delete": {"tags": ["UserPermissions"], "parameters": [{"name": "userId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "permissionId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/users": {"get": {"tags": ["Users"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/User"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/User"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/User"}}}}}}}, "post": {"tags": ["Users"], "parameters": [{"name": "UserID", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Username", "in": "query", "required": true, "schema": {"maxLength": 126, "type": "string"}}, {"name": "Email", "in": "query", "required": true, "schema": {"maxLength": 126, "type": "string", "format": "email"}}, {"name": "AzureId", "in": "query", "required": true, "schema": {"maxLength": 255, "type": "string"}}, {"name": "UserPermissions", "in": "query", "schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserPermission"}}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/User"}}, "application/json": {"schema": {"$ref": "#/components/schemas/User"}}, "text/json": {"schema": {"$ref": "#/components/schemas/User"}}}}}}}, "/api/users/{id}": {"get": {"tags": ["Users"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/User"}}, "application/json": {"schema": {"$ref": "#/components/schemas/User"}}, "text/json": {"schema": {"$ref": "#/components/schemas/User"}}}}}}, "put": {"tags": ["Users"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "UserID", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Username", "in": "query", "required": true, "schema": {"maxLength": 126, "type": "string"}}, {"name": "Email", "in": "query", "required": true, "schema": {"maxLength": 126, "type": "string", "format": "email"}}, {"name": "AzureId", "in": "query", "required": true, "schema": {"maxLength": 255, "type": "string"}}, {"name": "UserPermissions", "in": "query", "schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserPermission"}}}], "responses": {"200": {"description": "Success"}}}, "delete": {"tags": ["Users"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/users/update/{id}": {"post": {"tags": ["Users"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"Username": {"type": "string"}, "Email": {"type": "string"}}}, "encoding": {"Username": {"style": "form"}, "Email": {"style": "form"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/User"}}, "application/json": {"schema": {"$ref": "#/components/schemas/User"}}, "text/json": {"schema": {"$ref": "#/components/schemas/User"}}}}}}}, "/api/Vehicle": {"post": {"tags": ["Vehicle"], "summary": "Creates a new vehicle", "requestBody": {"description": "The new vehicle object", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/CreateVehicleDto"}, "examples": {"Ford": {"value": {"brandId": "0001", "brand": "Ford", "modelName": "Fiesta", "fuelType": "DIESEL", "version": "567", "year": 2022}}, "Honda": {"value": {"brandId": "0004", "brand": "Honda", "modelName": "Civic", "fuelType": "PETROL", "version": "123", "year": 2022}}}}, "application/json": {"schema": {"$ref": "#/components/schemas/CreateVehicleDto"}, "examples": {"Ford": {"value": {"brandId": "0001", "brand": "Ford", "modelName": "Fiesta", "fuelType": "DIESEL", "version": "567", "year": 2022}}, "Honda": {"value": {"brandId": "0004", "brand": "Honda", "modelName": "Civic", "fuelType": "PETROL", "version": "123", "year": 2022}}}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateVehicleDto"}, "examples": {"Ford": {"value": {"brandId": "0001", "brand": "Ford", "modelName": "Fiesta", "fuelType": "DIESEL", "version": "567", "year": 2022}}, "Honda": {"value": {"brandId": "0004", "brand": "Honda", "modelName": "Civic", "fuelType": "PETROL", "version": "123", "year": 2022}}}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateVehicleDto"}, "examples": {"Ford": {"value": {"brandId": "0001", "brand": "Ford", "modelName": "Fiesta", "fuelType": "DIESEL", "version": "567", "year": 2022}}, "Honda": {"value": {"brandId": "0004", "brand": "Honda", "modelName": "Civic", "fuelType": "PETROL", "version": "123", "year": 2022}}}}}}, "responses": {"201": {"description": "Returns the newly created vehicle", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VehicleDtoApiOutput"}, "example": {"code": 201, "description": null, "value": {"id": "9360534d-1f6d-4c10-902b-bb0c4a7b3008", "brandId": "0001", "brand": "Ford", "modelName": "Fiesta", "fuelType": "DIESEL", "version": "567", "year": 2022}, "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}}}, "400": {"description": "Malformed request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VehicleDtoApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}}}, "500": {"description": "Internal error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}}}}}, "get": {"tags": ["Vehicle"], "summary": "Gets all vehicles with custom filters", "parameters": [{"name": "id", "in": "query", "description": "Vehicle identifiers", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "brandId", "in": "query", "description": "Vehicle brands", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "modelName", "in": "query", "description": "Vehicle models", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "fuelType", "in": "query", "description": "Vehicle fuel types", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "version", "in": "query", "description": "Vehicle versions", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "year", "in": "query", "description": "Vehicle production year", "schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, {"name": "pageNumber", "in": "query", "description": "The page number.", "schema": {"type": "integer", "format": "int32"}}, {"name": "maxItems", "in": "query", "description": "The maximum number of items to be returned per request (may return a token to get the next page of results)", "schema": {"type": "integer", "format": "int32"}}, {"name": "sortField", "in": "query", "description": "The sort field.", "schema": {"type": "string"}}, {"name": "sortAscending", "in": "query", "description": "if set to `true` [sort ascending].", "schema": {"type": "boolean"}}, {"name": "continuationToken", "in": "query", "description": "The token to present the next page of results (URLEncoded)", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns a list of vehicles", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VehicleDtoListApiOutput"}, "example": {"code": 200, "description": null, "value": [{"id": "2ff2a3fd-4e45-4d9c-a313-56f3a7a89a76", "brandId": "0004", "brand": "Honda", "modelName": "Civic", "fuelType": "PETROL", "version": "123", "year": 2022}, {"id": "a7d3815a-f135-4d36-880d-19668535e00c", "brandId": "0004", "brand": "Honda", "modelName": "Accord", "fuelType": "PETROL", "version": "322", "year": 1998}], "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}}}, "400": {"description": "Malformed request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}}}, "500": {"description": "Internal error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}}}}}}, "/api/Vehicle/many": {"post": {"tags": ["Vehicle"], "summary": "Creates many new vehicles\r\nCaution: Only objects within the same partition may be created\r\nIF UNSURE DO NOT USE", "requestBody": {"description": "The list of new vehicle objects", "content": {"application/json-patch+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CreateVehicleDto"}}, "examples": {"Ford": {"value": [{"brandId": "0001", "brand": "Ford", "modelName": "Fiesta", "fuelType": "DIESEL", "version": "567", "year": 2022}, {"brandId": "0001", "brand": "Ford", "modelName": "Focus", "fuelType": "PETROL", "version": "223", "year": 2021}]}, "Honda": {"value": [{"brandId": "0004", "brand": "Honda", "modelName": "Civic", "fuelType": "PETROL", "version": "123", "year": 2022}, {"brandId": "0004", "brand": "Honda", "modelName": "Accord", "fuelType": "PETROL", "version": "322", "year": 1998}]}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CreateVehicleDto"}}, "examples": {"Ford": {"value": [{"brandId": "0001", "brand": "Ford", "modelName": "Fiesta", "fuelType": "DIESEL", "version": "567", "year": 2022}, {"brandId": "0001", "brand": "Ford", "modelName": "Focus", "fuelType": "PETROL", "version": "223", "year": 2021}]}, "Honda": {"value": [{"brandId": "0004", "brand": "Honda", "modelName": "Civic", "fuelType": "PETROL", "version": "123", "year": 2022}, {"brandId": "0004", "brand": "Honda", "modelName": "Accord", "fuelType": "PETROL", "version": "322", "year": 1998}]}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CreateVehicleDto"}}, "examples": {"Ford": {"value": [{"brandId": "0001", "brand": "Ford", "modelName": "Fiesta", "fuelType": "DIESEL", "version": "567", "year": 2022}, {"brandId": "0001", "brand": "Ford", "modelName": "Focus", "fuelType": "PETROL", "version": "223", "year": 2021}]}, "Honda": {"value": [{"brandId": "0004", "brand": "Honda", "modelName": "Civic", "fuelType": "PETROL", "version": "123", "year": 2022}, {"brandId": "0004", "brand": "Honda", "modelName": "Accord", "fuelType": "PETROL", "version": "322", "year": 1998}]}}}, "application/*+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CreateVehicleDto"}}, "examples": {"Ford": {"value": [{"brandId": "0001", "brand": "Ford", "modelName": "Fiesta", "fuelType": "DIESEL", "version": "567", "year": 2022}, {"brandId": "0001", "brand": "Ford", "modelName": "Focus", "fuelType": "PETROL", "version": "223", "year": 2021}]}, "Honda": {"value": [{"brandId": "0004", "brand": "Honda", "modelName": "Civic", "fuelType": "PETROL", "version": "123", "year": 2022}, {"brandId": "0004", "brand": "Honda", "modelName": "Accord", "fuelType": "PETROL", "version": "322", "year": 1998}]}}}}}, "responses": {"201": {"description": "Returns the newly created vehicles", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VehicleDtoListApiOutput"}, "example": {"code": 201, "description": null, "value": [{"id": "893863a5-0770-48ce-92ed-64189b498942", "brandId": "0004", "brand": "Honda", "modelName": "Civic", "fuelType": "PETROL", "version": "123", "year": 2022}, {"id": "0e07c266-7d9a-4c4b-a681-6efb0e819ced", "brandId": "0004", "brand": "Honda", "modelName": "Accord", "fuelType": "PETROL", "version": "322", "year": 1998}], "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}}}, "400": {"description": "Malformed request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}}}, "500": {"description": "Internal error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}}}}}}, "/api/Vehicle/addorupdate": {"post": {"tags": ["Vehicle"], "summary": "Adds or updates a vehicle\r\nCaution: If the ID is not found, it will create a new object\r\nIt is safer and more canonical to use separate endpoints", "requestBody": {"description": "The vehicle", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/VehicleDto"}, "example": {"id": "f9fb78f0-beb6-482c-abf4-b5d37526e5bc", "brandId": "0004", "brand": "Honda", "modelName": "Accord", "fuelType": "PETROL", "version": "322", "year": 1998}}, "application/json": {"schema": {"$ref": "#/components/schemas/VehicleDto"}, "example": {"id": "f9fb78f0-beb6-482c-abf4-b5d37526e5bc", "brandId": "0004", "brand": "Honda", "modelName": "Accord", "fuelType": "PETROL", "version": "322", "year": 1998}}, "text/json": {"schema": {"$ref": "#/components/schemas/VehicleDto"}, "example": {"id": "f9fb78f0-beb6-482c-abf4-b5d37526e5bc", "brandId": "0004", "brand": "Honda", "modelName": "Accord", "fuelType": "PETROL", "version": "322", "year": 1998}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VehicleDto"}, "example": {"id": "f9fb78f0-beb6-482c-abf4-b5d37526e5bc", "brandId": "0004", "brand": "Honda", "modelName": "Accord", "fuelType": "PETROL", "version": "322", "year": 1998}}}}, "responses": {"200": {"description": "Returns the newly created vehicles", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VehicleDtoApiOutput"}, "example": {"code": 200, "description": null, "value": {"id": "0c4e0b89-90c7-4b4a-b74d-b659c3f090b0", "brandId": "0001", "brand": "Ford", "modelName": "Fiesta", "fuelType": "DIESEL", "version": "567", "year": 2022}, "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {"etag": "\"00000107-0000-0700-0000-63652f960000\""}}}}}, "400": {"description": "Malformed request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}}}, "500": {"description": "Internal error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}}}}}}, "/api/Vehicle/{id}": {"put": {"tags": ["Vehicle"], "summary": "Updates a vehicle", "parameters": [{"name": "id", "in": "path", "description": "The vehicle identifier", "required": true, "schema": {"type": "string"}}, {"name": "etag", "in": "query", "description": "The latest write tag (in concurrency cases, to avoid overwriting the existing document)", "schema": {"type": "string"}}], "requestBody": {"description": "The vehicle to update", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/VehicleDto"}, "example": {"id": "3ad324bb-539b-4e76-868b-d346e96f4d84", "brandId": "0004", "brand": "Honda", "modelName": "Accord", "fuelType": "PETROL", "version": "322", "year": 1998}}, "application/json": {"schema": {"$ref": "#/components/schemas/VehicleDto"}, "example": {"id": "3ad324bb-539b-4e76-868b-d346e96f4d84", "brandId": "0004", "brand": "Honda", "modelName": "Accord", "fuelType": "PETROL", "version": "322", "year": 1998}}, "text/json": {"schema": {"$ref": "#/components/schemas/VehicleDto"}, "example": {"id": "3ad324bb-539b-4e76-868b-d346e96f4d84", "brandId": "0004", "brand": "Honda", "modelName": "Accord", "fuelType": "PETROL", "version": "322", "year": 1998}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VehicleDto"}, "example": {"id": "3ad324bb-539b-4e76-868b-d346e96f4d84", "brandId": "0004", "brand": "Honda", "modelName": "Accord", "fuelType": "PETROL", "version": "322", "year": 1998}}}}, "responses": {"200": {"description": "Returns the newly created vehicles", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VehicleDtoApiOutput"}, "example": {"code": 200, "description": null, "value": {"id": "4dd679dc-6c27-4091-9065-ab727<PERSON><PERSON><PERSON>", "brandId": "0001", "brand": "Ford", "modelName": "Fiesta", "fuelType": "DIESEL", "version": "567", "year": 2022}, "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {"etag": "\"00000107-0000-0700-0000-63652f960000\""}}}}}, "400": {"description": "Malformed request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}}}, "404": {"description": "Vehicle to update not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Not Found", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}}}, "409": {"description": "Vehicle to update changed in another context", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 409, "description": "Conflict", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}}}, "500": {"description": "Internal error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}}}}}, "patch": {"tags": ["Vehicle"], "summary": "Updates a vehicle", "parameters": [{"name": "id", "in": "path", "description": "The vehicle identifier", "required": true, "schema": {"type": "string"}}], "requestBody": {"description": "The changes", "content": {"application/json-patch+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Operation"}}, "example": [{"value": "Nissan", "path": "/brand", "op": "replace"}, {"path": "/version", "op": "remove"}, {"value": "1234", "path": "/version", "op": "add"}]}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Operation"}}, "example": [{"value": "Nissan", "path": "/brand", "op": "replace"}, {"path": "/version", "op": "remove"}, {"value": "1234", "path": "/version", "op": "add"}]}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Operation"}}, "example": [{"value": "Nissan", "path": "/brand", "op": "replace"}, {"path": "/version", "op": "remove"}, {"value": "1234", "path": "/version", "op": "add"}]}, "application/*+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Operation"}}, "example": [{"value": "Nissan", "path": "/brand", "op": "replace"}, {"path": "/version", "op": "remove"}, {"value": "1234", "path": "/version", "op": "add"}]}}}, "responses": {"200": {"description": "Returns the updated vehicle", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VehicleDtoApiOutput"}, "example": {"code": 200, "description": null, "value": {"id": "cdd2eb05-df1a-4e5c-b885-8a4159c5b1f0", "brandId": "0001", "brand": "Ford", "modelName": "Fiesta", "fuelType": "DIESEL", "version": "567", "year": 2022}, "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {"etag": "\"00000107-0000-0700-0000-63652f960000\""}}}}}, "400": {"description": "Malformed request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}}}, "404": {"description": "Vehicle to patch not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Not Found", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}}}, "500": {"description": "Internal error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}}}}}, "delete": {"tags": ["Vehicle"], "summary": "Deletes a vehicle", "parameters": [{"name": "id", "in": "path", "description": "The vehicle identifier", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "Vehicle deleted", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Error updating entity.", "value": null, "error": true, "exceptionMessages": {"messages": ["PreconditionFailed - Error updating entity."], "hasMessages": true}, "properties": {}}}}}, "400": {"description": "Malformed request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}}}, "500": {"description": "Internal error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}}}}}, "get": {"tags": ["Vehicle"], "summary": "Gets a vehicle by ID", "parameters": [{"name": "id", "in": "path", "description": "The vehicle identifier", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns a vehicle", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VehicleDtoApiOutput"}, "example": {"code": 200, "description": null, "value": {"id": "e42dc1e7-e88a-4ec0-8eee-448883c7fd3c", "brandId": "0001", "brand": "Ford", "modelName": "Fiesta", "fuelType": "DIESEL", "version": "567", "year": 2022}, "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {"etag": "\"00000107-0000-0700-0000-63652f960000\""}}}}}, "400": {"description": "Malformed request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}}}, "404": {"description": "Vehicle not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Not Found", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}}}, "500": {"description": "Internal error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}}}}}}, "/api/WeatherForecast": {"post": {"tags": ["WeatherForecast"], "summary": "Creates a new weather", "requestBody": {"description": "The new weather object", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/CreateWeatherDto"}, "examples": {"Warsaw": {"value": {"temperature": -5, "summary": "Very Cold", "location": "Warsaw"}}, "Australia": {"value": {"temperature": 4, "summary": "Very hot", "location": "Australia"}}}}, "application/json": {"schema": {"$ref": "#/components/schemas/CreateWeatherDto"}, "examples": {"Warsaw": {"value": {"temperature": -5, "summary": "Very Cold", "location": "Warsaw"}}, "Australia": {"value": {"temperature": 4, "summary": "Very hot", "location": "Australia"}}}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateWeatherDto"}, "examples": {"Warsaw": {"value": {"temperature": -5, "summary": "Very Cold", "location": "Warsaw"}}, "Australia": {"value": {"temperature": 4, "summary": "Very hot", "location": "Australia"}}}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateWeatherDto"}, "examples": {"Warsaw": {"value": {"temperature": -5, "summary": "Very Cold", "location": "Warsaw"}}, "Australia": {"value": {"temperature": 4, "summary": "Very hot", "location": "Australia"}}}}}}, "responses": {"201": {"description": "Returns the newly created weather", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WeatherDtoApiOutput"}, "example": {"code": 201, "description": null, "value": {"id": "12d65e18-bb40-461b-9812-328b3542f919", "temperature": -5, "summary": "Very cold", "location": "Warsaw"}, "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}}}, "400": {"description": "Malformed request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}}}, "500": {"description": "Internal error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}}}}}, "get": {"tags": ["WeatherForecast"], "summary": "Gets all weathers with custom filters", "parameters": [{"name": "id", "in": "query", "description": "Weather identifier", "schema": {"type": "string", "format": "uuid"}}, {"name": "location", "in": "query", "description": "Location", "schema": {"type": "string"}}, {"name": "itemsPerPage", "in": "query", "description": "The number of items per page.", "schema": {"type": "integer", "format": "int32"}}, {"name": "page", "in": "query", "description": "The page.", "schema": {"type": "integer", "format": "int32"}}, {"name": "sortField", "in": "query", "description": "The sort field.", "schema": {"type": "string"}}, {"name": "sortAscending", "in": "query", "description": "if set to `true` [sort ascending].", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Returns a list of weathers", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherDto"}}, "example": {"code": 200, "description": null, "value": [{"id": "51db028a-97e0-4f1e-baaf-1814a43205a5", "temperature": 20, "summary": null, "location": "Lisboa Transaction"}, {"id": "4428ec27-73cb-455f-b8ff-c089946d750b", "temperature": 15, "summary": null, "location": "Porto Transaction"}], "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}}}, "400": {"description": "Malformed request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}}}, "404": {"description": "Weather not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Not Found", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}}}, "500": {"description": "Internal error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}}}}}}, "/api/WeatherForecast/transaction": {"post": {"tags": ["WeatherForecast"], "summary": "Test UoW transaction", "responses": {"201": {"description": "Returns the newly created weathers", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WeatherDtoListApiOutput"}, "example": {"code": 201, "description": null, "value": [{"id": "139e6e60-34e8-4583-819a-90dd56e64d4b", "temperature": 27, "summary": null, "location": "Lisbon"}, {"id": "51417231-75af-45f3-ac17-194b42f99b98", "temperature": 25, "summary": null, "location": "Porto"}], "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}}}, "400": {"description": "Malformed request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}}}, "500": {"description": "Internal error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}}}}}}, "/api/WeatherForecast/{id}": {"put": {"tags": ["WeatherForecast"], "summary": "Updates a weather", "parameters": [{"name": "id", "in": "path", "description": "The weather identifier", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "rowversion", "in": "query", "description": "The latest write tag (in concurrency cases, to avoid overwriting the existing document)", "schema": {"type": "string"}}], "requestBody": {"description": "The weather to update", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/WeatherDto"}, "example": {"id": "c7817698-bf37-42da-8718-11d57488b6eb", "temperature": 27, "summary": null, "location": "Lisbon"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WeatherDto"}, "example": {"id": "c7817698-bf37-42da-8718-11d57488b6eb", "temperature": 27, "summary": null, "location": "Lisbon"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WeatherDto"}, "example": {"id": "c7817698-bf37-42da-8718-11d57488b6eb", "temperature": 27, "summary": null, "location": "Lisbon"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WeatherDto"}, "example": {"id": "c7817698-bf37-42da-8718-11d57488b6eb", "temperature": 27, "summary": null, "location": "Lisbon"}}}}, "responses": {"200": {"description": "Returns the newly created weathers", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WeatherDtoApiOutput"}, "example": {"code": 200, "description": null, "value": {"id": "d84f9e70-364f-49b1-9c7f-f624dd504a8e", "temperature": 21, "summary": "Cloudy", "location": "<PERSON><PERSON><PERSON>"}, "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}}}, "400": {"description": "Malformed request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}}}, "404": {"description": "Weather not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Not Found", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}}}, "409": {"description": "Conflict on the request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 409, "description": "Conflict", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}}}, "500": {"description": "Internal error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}}}}}, "patch": {"tags": ["WeatherForecast"], "summary": "Updates a weather", "parameters": [{"name": "id", "in": "path", "description": "The weather identifier", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"description": "The changes", "content": {"application/json-patch+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Operation"}}, "example": [{"value": 21, "path": "/temperature", "op": "replace"}]}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Operation"}}, "example": [{"value": 21, "path": "/temperature", "op": "replace"}]}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Operation"}}, "example": [{"value": 21, "path": "/temperature", "op": "replace"}]}, "application/*+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Operation"}}, "example": [{"value": 21, "path": "/temperature", "op": "replace"}]}}}, "responses": {"200": {"description": "Returns the updated weather", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WeatherDtoApiOutput"}, "example": {"code": 200, "description": null, "value": {"id": "500259b4-9eef-432a-8cd1-b1196293f015", "temperature": 21, "summary": "Cloudy", "location": "<PERSON><PERSON><PERSON>"}, "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}}}, "400": {"description": "Malformed request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}}}, "404": {"description": "Weather not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Not Found", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}}}, "500": {"description": "Internal error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}}}}}, "delete": {"tags": ["WeatherForecast"], "summary": "Deletes a weather", "parameters": [{"name": "id", "in": "path", "description": "The weather identifier", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"400": {"description": "Malformed request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}}}, "500": {"description": "Internal error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}}}, "204": {"description": "The weather was deleted"}}}, "get": {"tags": ["WeatherForecast"], "summary": "Gets a weather by ID", "parameters": [{"name": "id", "in": "path", "description": "The weather identifier", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Returns a weather", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WeatherDtoApiOutput"}, "example": {"code": 200, "description": null, "value": {"id": "7a9fc378-f6ee-4731-ad4a-c2cd48b6ffca", "temperature": 21, "summary": "Cloudy", "location": "<PERSON><PERSON><PERSON>"}, "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}}}, "400": {"description": "Malformed request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}}}, "404": {"description": "Weather not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Not Found", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}}}, "500": {"description": "Internal error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}}}}}}, "/api/Workflow/definitions": {"get": {"tags": ["Workflow"], "summary": "Gets the workflow definitions.", "parameters": [{"name": "SourceType", "in": "query", "description": "Gets or sets the type of the source.", "schema": {"type": "string"}}, {"name": "WorkflowType", "in": "query", "description": "Gets or sets the type of the workflow.", "schema": {"$ref": "#/components/schemas/WorkflowType"}}, {"name": "FetchInactive", "in": "query", "description": "Gets or sets a value indicating whether [fetch inactive].", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Returns a List of definitions", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/WorkflowDefinitionDtoIListApiOutput"}, "example": {"code": 200, "description": null, "value": [{"id": "66549057-8934-4a59-bfb2-1fe4d9e15952", "displayName": "Not Billable Project Approval", "rulesConditionType": "ANY_RULE", "sourceType": "PROJECT", "workflowType": "AUTOMATIC", "purpose": "APPROVAL", "numberOfSteps": 1, "numberOfRules": 1, "isActive": true}, {"id": "47179d17-09c9-4624-8fac-3c57ff934cb0", "displayName": "Test Skip LR", "rulesConditionType": "ANY_RULE", "sourceType": "DOCUMENT", "workflowType": "MANUAL", "purpose": "APPROVAL", "numberOfSteps": 2, "numberOfRules": 0, "isActive": true}], "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}, "application/json": {"schema": {"$ref": "#/components/schemas/WorkflowDefinitionDtoIListApiOutput"}, "example": {"code": 200, "description": null, "value": [{"id": "66549057-8934-4a59-bfb2-1fe4d9e15952", "displayName": "Not Billable Project Approval", "rulesConditionType": "ANY_RULE", "sourceType": "PROJECT", "workflowType": "AUTOMATIC", "purpose": "APPROVAL", "numberOfSteps": 1, "numberOfRules": 1, "isActive": true}, {"id": "47179d17-09c9-4624-8fac-3c57ff934cb0", "displayName": "Test Skip LR", "rulesConditionType": "ANY_RULE", "sourceType": "DOCUMENT", "workflowType": "MANUAL", "purpose": "APPROVAL", "numberOfSteps": 2, "numberOfRules": 0, "isActive": true}], "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}, "text/json": {"schema": {"$ref": "#/components/schemas/WorkflowDefinitionDtoIListApiOutput"}, "example": {"code": 200, "description": null, "value": [{"id": "66549057-8934-4a59-bfb2-1fe4d9e15952", "displayName": "Not Billable Project Approval", "rulesConditionType": "ANY_RULE", "sourceType": "PROJECT", "workflowType": "AUTOMATIC", "purpose": "APPROVAL", "numberOfSteps": 1, "numberOfRules": 1, "isActive": true}, {"id": "47179d17-09c9-4624-8fac-3c57ff934cb0", "displayName": "Test Skip LR", "rulesConditionType": "ANY_RULE", "sourceType": "DOCUMENT", "workflowType": "MANUAL", "purpose": "APPROVAL", "numberOfSteps": 2, "numberOfRules": 0, "isActive": true}], "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}}}, "400": {"description": "Malformed request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}}}, "500": {"description": "Internal error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}, "application/json": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}, "text/json": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}}}, "204": {"description": "No content response"}, "401": {"description": "User Unauthorized"}}}}, "/api/Workflow/instances/list": {"post": {"tags": ["Workflow"], "summary": "Gets the list of workflow instances by a list of sourceIds and/or by a list of status.", "requestBody": {"description": "The request.", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/WorkflowInstanceBySourceIdsDto"}, "examples": {"Multiple SourceIds": {"value": {"sourceIds": ["Vacation/2023", "Project/2023"], "status": ["IN_PROGRESS"]}}, "Without Status": {"value": {"sourceIds": ["Vacation/2023", "Project/2023"], "status": null}}, "Multiple Status": {"value": {"sourceIds": ["Vacation/2023", "Project/2023"], "status": ["IN_PROGRESS", "CANCELLED"]}}}}, "application/json": {"schema": {"$ref": "#/components/schemas/WorkflowInstanceBySourceIdsDto"}, "examples": {"Multiple SourceIds": {"value": {"sourceIds": ["Vacation/2023", "Project/2023"], "status": ["IN_PROGRESS"]}}, "Without Status": {"value": {"sourceIds": ["Vacation/2023", "Project/2023"], "status": null}}, "Multiple Status": {"value": {"sourceIds": ["Vacation/2023", "Project/2023"], "status": ["IN_PROGRESS", "CANCELLED"]}}}}, "text/json": {"schema": {"$ref": "#/components/schemas/WorkflowInstanceBySourceIdsDto"}, "examples": {"Multiple SourceIds": {"value": {"sourceIds": ["Vacation/2023", "Project/2023"], "status": ["IN_PROGRESS"]}}, "Without Status": {"value": {"sourceIds": ["Vacation/2023", "Project/2023"], "status": null}}, "Multiple Status": {"value": {"sourceIds": ["Vacation/2023", "Project/2023"], "status": ["IN_PROGRESS", "CANCELLED"]}}}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WorkflowInstanceBySourceIdsDto"}, "examples": {"Multiple SourceIds": {"value": {"sourceIds": ["Vacation/2023", "Project/2023"], "status": ["IN_PROGRESS"]}}, "Without Status": {"value": {"sourceIds": ["Vacation/2023", "Project/2023"], "status": null}}, "Multiple Status": {"value": {"sourceIds": ["Vacation/2023", "Project/2023"], "status": ["IN_PROGRESS", "CANCELLED"]}}}}}}, "responses": {"200": {"description": "Returns a List of Instances by a list of sourceIds and/or by a list of status", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/WorkflowInstanceDtoIListApiOutput"}, "example": {"code": 200, "description": null, "value": [{"id": "1949db44-5cd7-409c-9244-07a5210fa56f", "workflowId": "00000000-0000-0000-0000-000000000000", "displayName": "Not Billable Project Approval", "sourceId": "Project/2023", "sourceUrl": null, "workflowStatus": "IN_PROGRESS", "sourceType": "DOCUMENT", "workflowType": "AUTOMATIC", "purpose": "APPROVAL", "stateProcessingRequired": false, "activeStepName": "Approval", "steps": [{"id": "53ab3926-3311-4565-b732-dbe2b8d156ac", "stepId": null, "instanceId": "1949db44-5cd7-409c-9244-07a5210fa56f", "displayName": "Approval", "allUsersMustApprove": true, "skipOverOtherTasks": false, "order": 1, "isActive": true, "stepStatus": "TODO", "numberOfActionsRequired": 2, "numberOfActionsCompleted": 0, "tasks": [{"id": "5c84086e-1bd7-4b06-b6f7-e17df63b8cd3", "userId": "<EMAIL>", "taskStatus": "TODO", "taskCompleted": false, "taskActivated": true, "comments": "", "sysModifyDate": "2023-01-18T15:59:42.457+00:00"}, {"id": "f80c4b6d-61ce-406a-8cd1-d66361adc977", "userId": "<EMAIL>", "taskStatus": "TODO", "taskCompleted": false, "taskActivated": true, "comments": "", "sysModifyDate": "2023-01-18T15:59:43.713+00:00"}]}], "sysCreateUserId": null, "sysModifyDate": "0001-01-01T00:00:00+00:00"}], "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}, "application/json": {"schema": {"$ref": "#/components/schemas/WorkflowInstanceDtoIListApiOutput"}, "example": {"code": 200, "description": null, "value": [{"id": "1949db44-5cd7-409c-9244-07a5210fa56f", "workflowId": "00000000-0000-0000-0000-000000000000", "displayName": "Not Billable Project Approval", "sourceId": "Project/2023", "sourceUrl": null, "workflowStatus": "IN_PROGRESS", "sourceType": "DOCUMENT", "workflowType": "AUTOMATIC", "purpose": "APPROVAL", "stateProcessingRequired": false, "activeStepName": "Approval", "steps": [{"id": "53ab3926-3311-4565-b732-dbe2b8d156ac", "stepId": null, "instanceId": "1949db44-5cd7-409c-9244-07a5210fa56f", "displayName": "Approval", "allUsersMustApprove": true, "skipOverOtherTasks": false, "order": 1, "isActive": true, "stepStatus": "TODO", "numberOfActionsRequired": 2, "numberOfActionsCompleted": 0, "tasks": [{"id": "5c84086e-1bd7-4b06-b6f7-e17df63b8cd3", "userId": "<EMAIL>", "taskStatus": "TODO", "taskCompleted": false, "taskActivated": true, "comments": "", "sysModifyDate": "2023-01-18T15:59:42.457+00:00"}, {"id": "f80c4b6d-61ce-406a-8cd1-d66361adc977", "userId": "<EMAIL>", "taskStatus": "TODO", "taskCompleted": false, "taskActivated": true, "comments": "", "sysModifyDate": "2023-01-18T15:59:43.713+00:00"}]}], "sysCreateUserId": null, "sysModifyDate": "0001-01-01T00:00:00+00:00"}], "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}, "text/json": {"schema": {"$ref": "#/components/schemas/WorkflowInstanceDtoIListApiOutput"}, "example": {"code": 200, "description": null, "value": [{"id": "1949db44-5cd7-409c-9244-07a5210fa56f", "workflowId": "00000000-0000-0000-0000-000000000000", "displayName": "Not Billable Project Approval", "sourceId": "Project/2023", "sourceUrl": null, "workflowStatus": "IN_PROGRESS", "sourceType": "DOCUMENT", "workflowType": "AUTOMATIC", "purpose": "APPROVAL", "stateProcessingRequired": false, "activeStepName": "Approval", "steps": [{"id": "53ab3926-3311-4565-b732-dbe2b8d156ac", "stepId": null, "instanceId": "1949db44-5cd7-409c-9244-07a5210fa56f", "displayName": "Approval", "allUsersMustApprove": true, "skipOverOtherTasks": false, "order": 1, "isActive": true, "stepStatus": "TODO", "numberOfActionsRequired": 2, "numberOfActionsCompleted": 0, "tasks": [{"id": "5c84086e-1bd7-4b06-b6f7-e17df63b8cd3", "userId": "<EMAIL>", "taskStatus": "TODO", "taskCompleted": false, "taskActivated": true, "comments": "", "sysModifyDate": "2023-01-18T15:59:42.457+00:00"}, {"id": "f80c4b6d-61ce-406a-8cd1-d66361adc977", "userId": "<EMAIL>", "taskStatus": "TODO", "taskCompleted": false, "taskActivated": true, "comments": "", "sysModifyDate": "2023-01-18T15:59:43.713+00:00"}]}], "sysCreateUserId": null, "sysModifyDate": "0001-01-01T00:00:00+00:00"}], "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}}}, "400": {"description": "Malformed request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}}}, "500": {"description": "Internal error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}, "application/json": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}, "text/json": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}}}, "204": {"description": "No content response"}, "401": {"description": "User Unauthorized"}}}}, "/api/Workflow/instances": {"get": {"tags": ["Workflow"], "summary": "Gets the list of workflow instances initiated by current user.", "parameters": [{"name": "SourceType", "in": "query", "description": "Gets or sets the type of the source.", "schema": {"type": "string"}}, {"name": "WorkflowStatus", "in": "query", "description": "Gets or sets the workflow status.", "schema": {"$ref": "#/components/schemas/WorkflowStatus"}}, {"name": "WorkflowType", "in": "query", "description": "Gets or sets the type of the workflow.", "schema": {"$ref": "#/components/schemas/WorkflowType"}}, {"name": "WorkflowName", "in": "query", "description": "Gets or sets the name of the workflow.", "schema": {"type": "string"}}, {"name": "Purpose", "in": "query", "description": "Gets or sets the purpose.", "schema": {"$ref": "#/components/schemas/WorkflowPurpose"}}, {"name": "SortField", "in": "query", "description": "Gets or sets the sort field.", "schema": {"type": "string"}}, {"name": "SortDirection", "in": "query", "description": "Gets or sets the sort direction.", "schema": {"type": "string"}}, {"name": "ItemsPerPage", "in": "query", "description": "Gets or sets the items per page.", "schema": {"type": "integer", "format": "int32"}}, {"name": "CurrentPage", "in": "query", "description": "Gets or sets the current page.", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Returns a list of User Instances", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/WorkflowInstancesListDtoIListApiOutput"}, "example": {"code": 200, "description": null, "value": {"workflowInstances": [{"id": "1949db44-5cd7-409c-9244-07a5210fa56f", "workflowId": "00000000-0000-0000-0000-000000000000", "displayName": "Not Billable Project Approval", "sourceId": "Project/2023", "sourceUrl": null, "workflowStatus": "IN_PROGRESS", "sourceType": "DOCUMENT", "workflowType": "AUTOMATIC", "purpose": "APPROVAL", "stateProcessingRequired": false, "activeStepName": "Approval", "steps": [{"id": "53ab3926-3311-4565-b732-dbe2b8d156ac", "stepId": null, "instanceId": "1949db44-5cd7-409c-9244-07a5210fa56f", "displayName": "Approval", "allUsersMustApprove": true, "skipOverOtherTasks": false, "order": 1, "isActive": true, "stepStatus": "TODO", "numberOfActionsRequired": 2, "numberOfActionsCompleted": 0, "tasks": [{"id": "5c84086e-1bd7-4b06-b6f7-e17df63b8cd3", "userId": "<EMAIL>", "taskStatus": "TODO", "taskCompleted": false, "taskActivated": true, "comments": "", "sysModifyDate": "2023-01-18T15:59:42.457+00:00"}, {"id": "f80c4b6d-61ce-406a-8cd1-d66361adc977", "userId": "<EMAIL>", "taskStatus": "TODO", "taskCompleted": false, "taskActivated": true, "comments": "", "sysModifyDate": "2023-01-18T15:59:43.713+00:00"}]}], "sysCreateUserId": null, "sysModifyDate": "0001-01-01T00:00:00+00:00"}], "totalRows": 1}, "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}, "application/json": {"schema": {"$ref": "#/components/schemas/WorkflowInstancesListDtoIListApiOutput"}, "example": {"code": 200, "description": null, "value": {"workflowInstances": [{"id": "1949db44-5cd7-409c-9244-07a5210fa56f", "workflowId": "00000000-0000-0000-0000-000000000000", "displayName": "Not Billable Project Approval", "sourceId": "Project/2023", "sourceUrl": null, "workflowStatus": "IN_PROGRESS", "sourceType": "DOCUMENT", "workflowType": "AUTOMATIC", "purpose": "APPROVAL", "stateProcessingRequired": false, "activeStepName": "Approval", "steps": [{"id": "53ab3926-3311-4565-b732-dbe2b8d156ac", "stepId": null, "instanceId": "1949db44-5cd7-409c-9244-07a5210fa56f", "displayName": "Approval", "allUsersMustApprove": true, "skipOverOtherTasks": false, "order": 1, "isActive": true, "stepStatus": "TODO", "numberOfActionsRequired": 2, "numberOfActionsCompleted": 0, "tasks": [{"id": "5c84086e-1bd7-4b06-b6f7-e17df63b8cd3", "userId": "<EMAIL>", "taskStatus": "TODO", "taskCompleted": false, "taskActivated": true, "comments": "", "sysModifyDate": "2023-01-18T15:59:42.457+00:00"}, {"id": "f80c4b6d-61ce-406a-8cd1-d66361adc977", "userId": "<EMAIL>", "taskStatus": "TODO", "taskCompleted": false, "taskActivated": true, "comments": "", "sysModifyDate": "2023-01-18T15:59:43.713+00:00"}]}], "sysCreateUserId": null, "sysModifyDate": "0001-01-01T00:00:00+00:00"}], "totalRows": 1}, "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}, "text/json": {"schema": {"$ref": "#/components/schemas/WorkflowInstancesListDtoIListApiOutput"}, "example": {"code": 200, "description": null, "value": {"workflowInstances": [{"id": "1949db44-5cd7-409c-9244-07a5210fa56f", "workflowId": "00000000-0000-0000-0000-000000000000", "displayName": "Not Billable Project Approval", "sourceId": "Project/2023", "sourceUrl": null, "workflowStatus": "IN_PROGRESS", "sourceType": "DOCUMENT", "workflowType": "AUTOMATIC", "purpose": "APPROVAL", "stateProcessingRequired": false, "activeStepName": "Approval", "steps": [{"id": "53ab3926-3311-4565-b732-dbe2b8d156ac", "stepId": null, "instanceId": "1949db44-5cd7-409c-9244-07a5210fa56f", "displayName": "Approval", "allUsersMustApprove": true, "skipOverOtherTasks": false, "order": 1, "isActive": true, "stepStatus": "TODO", "numberOfActionsRequired": 2, "numberOfActionsCompleted": 0, "tasks": [{"id": "5c84086e-1bd7-4b06-b6f7-e17df63b8cd3", "userId": "<EMAIL>", "taskStatus": "TODO", "taskCompleted": false, "taskActivated": true, "comments": "", "sysModifyDate": "2023-01-18T15:59:42.457+00:00"}, {"id": "f80c4b6d-61ce-406a-8cd1-d66361adc977", "userId": "<EMAIL>", "taskStatus": "TODO", "taskCompleted": false, "taskActivated": true, "comments": "", "sysModifyDate": "2023-01-18T15:59:43.713+00:00"}]}], "sysCreateUserId": null, "sysModifyDate": "0001-01-01T00:00:00+00:00"}], "totalRows": 1}, "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}}}, "500": {"description": "Internal error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}, "application/json": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}, "text/json": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}}}, "204": {"description": "No content response"}, "401": {"description": "User Unauthorized"}}}}, "/api/Workflow/instance/{id}": {"get": {"tags": ["Workflow"], "summary": "Gets the workflow instance by identifier.", "parameters": [{"name": "id", "in": "path", "description": "The identifier.", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns a Instance by Id", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/WorkflowInstanceDtoApiOutput"}, "example": {"code": 200, "description": null, "value": [{"id": "1949db44-5cd7-409c-9244-07a5210fa56f", "workflowId": "00000000-0000-0000-0000-000000000000", "displayName": "Not Billable Project Approval", "sourceId": "Project/2023", "sourceUrl": null, "workflowStatus": "IN_PROGRESS", "sourceType": "DOCUMENT", "workflowType": "AUTOMATIC", "purpose": "APPROVAL", "stateProcessingRequired": false, "activeStepName": "Approval", "steps": [{"id": "53ab3926-3311-4565-b732-dbe2b8d156ac", "stepId": null, "instanceId": "1949db44-5cd7-409c-9244-07a5210fa56f", "displayName": "Approval", "allUsersMustApprove": true, "skipOverOtherTasks": false, "order": 1, "isActive": true, "stepStatus": "TODO", "numberOfActionsRequired": 2, "numberOfActionsCompleted": 0, "tasks": [{"id": "5c84086e-1bd7-4b06-b6f7-e17df63b8cd3", "userId": "<EMAIL>", "taskStatus": "TODO", "taskCompleted": false, "taskActivated": true, "comments": "", "sysModifyDate": "2023-01-18T15:59:42.457+00:00"}, {"id": "f80c4b6d-61ce-406a-8cd1-d66361adc977", "userId": "<EMAIL>", "taskStatus": "TODO", "taskCompleted": false, "taskActivated": true, "comments": "", "sysModifyDate": "2023-01-18T15:59:43.713+00:00"}]}], "sysCreateUserId": null, "sysModifyDate": "0001-01-01T00:00:00+00:00"}], "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}, "application/json": {"schema": {"$ref": "#/components/schemas/WorkflowInstanceDtoApiOutput"}, "example": {"code": 200, "description": null, "value": [{"id": "1949db44-5cd7-409c-9244-07a5210fa56f", "workflowId": "00000000-0000-0000-0000-000000000000", "displayName": "Not Billable Project Approval", "sourceId": "Project/2023", "sourceUrl": null, "workflowStatus": "IN_PROGRESS", "sourceType": "DOCUMENT", "workflowType": "AUTOMATIC", "purpose": "APPROVAL", "stateProcessingRequired": false, "activeStepName": "Approval", "steps": [{"id": "53ab3926-3311-4565-b732-dbe2b8d156ac", "stepId": null, "instanceId": "1949db44-5cd7-409c-9244-07a5210fa56f", "displayName": "Approval", "allUsersMustApprove": true, "skipOverOtherTasks": false, "order": 1, "isActive": true, "stepStatus": "TODO", "numberOfActionsRequired": 2, "numberOfActionsCompleted": 0, "tasks": [{"id": "5c84086e-1bd7-4b06-b6f7-e17df63b8cd3", "userId": "<EMAIL>", "taskStatus": "TODO", "taskCompleted": false, "taskActivated": true, "comments": "", "sysModifyDate": "2023-01-18T15:59:42.457+00:00"}, {"id": "f80c4b6d-61ce-406a-8cd1-d66361adc977", "userId": "<EMAIL>", "taskStatus": "TODO", "taskCompleted": false, "taskActivated": true, "comments": "", "sysModifyDate": "2023-01-18T15:59:43.713+00:00"}]}], "sysCreateUserId": null, "sysModifyDate": "0001-01-01T00:00:00+00:00"}], "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}, "text/json": {"schema": {"$ref": "#/components/schemas/WorkflowInstanceDtoApiOutput"}, "example": {"code": 200, "description": null, "value": [{"id": "1949db44-5cd7-409c-9244-07a5210fa56f", "workflowId": "00000000-0000-0000-0000-000000000000", "displayName": "Not Billable Project Approval", "sourceId": "Project/2023", "sourceUrl": null, "workflowStatus": "IN_PROGRESS", "sourceType": "DOCUMENT", "workflowType": "AUTOMATIC", "purpose": "APPROVAL", "stateProcessingRequired": false, "activeStepName": "Approval", "steps": [{"id": "53ab3926-3311-4565-b732-dbe2b8d156ac", "stepId": null, "instanceId": "1949db44-5cd7-409c-9244-07a5210fa56f", "displayName": "Approval", "allUsersMustApprove": true, "skipOverOtherTasks": false, "order": 1, "isActive": true, "stepStatus": "TODO", "numberOfActionsRequired": 2, "numberOfActionsCompleted": 0, "tasks": [{"id": "5c84086e-1bd7-4b06-b6f7-e17df63b8cd3", "userId": "<EMAIL>", "taskStatus": "TODO", "taskCompleted": false, "taskActivated": true, "comments": "", "sysModifyDate": "2023-01-18T15:59:42.457+00:00"}, {"id": "f80c4b6d-61ce-406a-8cd1-d66361adc977", "userId": "<EMAIL>", "taskStatus": "TODO", "taskCompleted": false, "taskActivated": true, "comments": "", "sysModifyDate": "2023-01-18T15:59:43.713+00:00"}]}], "sysCreateUserId": null, "sysModifyDate": "0001-01-01T00:00:00+00:00"}], "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}}}, "400": {"description": "Malformed request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}}}, "500": {"description": "Internal error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}, "application/json": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}, "text/json": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}}}, "204": {"description": "No content response"}, "401": {"description": "User Unauthorized"}}}}, "/api/Workflow/instance/{instanceId}/{userId}": {"delete": {"tags": ["Workflow"], "summary": "Cancels the workflow instance.", "parameters": [{"name": "instanceId", "in": "path", "description": "The instance identifier.", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "userId", "in": "path", "description": "The user identifier.", "required": true, "schema": {"type": "string"}}], "requestBody": {"description": "The request.", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/WorkflowEmailDto"}, "examples": {"With Custom Email": {"value": {"templateId": "d-c43961e085e04f1c8e1f0192db1c69d8", "recipients": ["<EMAIL>", "<EMAIL>"], "templateData": {"Message": "This is an example of Email"}, "subject": "FrameWork Example of Email", "sendEmail": true}}, "Don't send e-mail": {"value": {"templateId": null, "recipients": null, "templateData": null, "subject": null, "sendEmail": false}}, "Send default WFE e-mail (same as with no request)": {"value": {"templateId": null, "recipients": null, "templateData": null, "subject": null, "sendEmail": true}}}}, "application/json": {"schema": {"$ref": "#/components/schemas/WorkflowEmailDto"}, "examples": {"With Custom Email": {"value": {"templateId": "d-c43961e085e04f1c8e1f0192db1c69d8", "recipients": ["<EMAIL>", "<EMAIL>"], "templateData": {"Message": "This is an example of Email"}, "subject": "FrameWork Example of Email", "sendEmail": true}}, "Don't send e-mail": {"value": {"templateId": null, "recipients": null, "templateData": null, "subject": null, "sendEmail": false}}, "Send default WFE e-mail (same as with no request)": {"value": {"templateId": null, "recipients": null, "templateData": null, "subject": null, "sendEmail": true}}}}, "text/json": {"schema": {"$ref": "#/components/schemas/WorkflowEmailDto"}, "examples": {"With Custom Email": {"value": {"templateId": "d-c43961e085e04f1c8e1f0192db1c69d8", "recipients": ["<EMAIL>", "<EMAIL>"], "templateData": {"Message": "This is an example of Email"}, "subject": "FrameWork Example of Email", "sendEmail": true}}, "Don't send e-mail": {"value": {"templateId": null, "recipients": null, "templateData": null, "subject": null, "sendEmail": false}}, "Send default WFE e-mail (same as with no request)": {"value": {"templateId": null, "recipients": null, "templateData": null, "subject": null, "sendEmail": true}}}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WorkflowEmailDto"}, "examples": {"With Custom Email": {"value": {"templateId": "d-c43961e085e04f1c8e1f0192db1c69d8", "recipients": ["<EMAIL>", "<EMAIL>"], "templateData": {"Message": "This is an example of Email"}, "subject": "FrameWork Example of Email", "sendEmail": true}}, "Don't send e-mail": {"value": {"templateId": null, "recipients": null, "templateData": null, "subject": null, "sendEmail": false}}, "Send default WFE e-mail (same as with no request)": {"value": {"templateId": null, "recipients": null, "templateData": null, "subject": null, "sendEmail": true}}}}}}, "responses": {"200": {"description": "Returns True if sucess, false otherwise", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanApiOutput"}, "example": {"code": 200, "description": null, "value": true, "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanApiOutput"}, "example": {"code": 200, "description": null, "value": true, "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanApiOutput"}, "example": {"code": 200, "description": null, "value": true, "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}}}, "400": {"description": "Malformed request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}}}, "500": {"description": "Internal error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}, "application/json": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}, "text/json": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}}}, "401": {"description": "User Unauthorized"}}}}, "/api/Workflow/instances/deleteOrCancel/{sourceId}": {"delete": {"tags": ["Workflow"], "summary": "Deletes the or cancel workflows by source identifier.", "parameters": [{"name": "sourceId", "in": "path", "description": "The source identifier.", "required": true, "schema": {"type": "string"}}, {"name": "isDeleteOperation", "in": "query", "description": "if set to `true` [is delete operation].", "schema": {"type": "boolean", "default": false}}], "requestBody": {"description": "The request.", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/WorkflowEmailDto"}, "examples": {"With Custom Email": {"value": {"templateId": "d-c43961e085e04f1c8e1f0192db1c69d8", "recipients": ["<EMAIL>", "<EMAIL>"], "templateData": {"Message": "This is an example of Email"}, "subject": "FrameWork Example of Email", "sendEmail": true}}, "Don't send e-mail": {"value": {"templateId": null, "recipients": null, "templateData": null, "subject": null, "sendEmail": false}}, "Send default WFE e-mail (same as with no request)": {"value": {"templateId": null, "recipients": null, "templateData": null, "subject": null, "sendEmail": true}}}}, "application/json": {"schema": {"$ref": "#/components/schemas/WorkflowEmailDto"}, "examples": {"With Custom Email": {"value": {"templateId": "d-c43961e085e04f1c8e1f0192db1c69d8", "recipients": ["<EMAIL>", "<EMAIL>"], "templateData": {"Message": "This is an example of Email"}, "subject": "FrameWork Example of Email", "sendEmail": true}}, "Don't send e-mail": {"value": {"templateId": null, "recipients": null, "templateData": null, "subject": null, "sendEmail": false}}, "Send default WFE e-mail (same as with no request)": {"value": {"templateId": null, "recipients": null, "templateData": null, "subject": null, "sendEmail": true}}}}, "text/json": {"schema": {"$ref": "#/components/schemas/WorkflowEmailDto"}, "examples": {"With Custom Email": {"value": {"templateId": "d-c43961e085e04f1c8e1f0192db1c69d8", "recipients": ["<EMAIL>", "<EMAIL>"], "templateData": {"Message": "This is an example of Email"}, "subject": "FrameWork Example of Email", "sendEmail": true}}, "Don't send e-mail": {"value": {"templateId": null, "recipients": null, "templateData": null, "subject": null, "sendEmail": false}}, "Send default WFE e-mail (same as with no request)": {"value": {"templateId": null, "recipients": null, "templateData": null, "subject": null, "sendEmail": true}}}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WorkflowEmailDto"}, "examples": {"With Custom Email": {"value": {"templateId": "d-c43961e085e04f1c8e1f0192db1c69d8", "recipients": ["<EMAIL>", "<EMAIL>"], "templateData": {"Message": "This is an example of Email"}, "subject": "FrameWork Example of Email", "sendEmail": true}}, "Don't send e-mail": {"value": {"templateId": null, "recipients": null, "templateData": null, "subject": null, "sendEmail": false}}, "Send default WFE e-mail (same as with no request)": {"value": {"templateId": null, "recipients": null, "templateData": null, "subject": null, "sendEmail": true}}}}}}, "responses": {"200": {"description": "Returns True if sucess, false otherwise", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanApiOutput"}, "example": {"code": 200, "description": null, "value": true, "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanApiOutput"}, "example": {"code": 200, "description": null, "value": true, "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanApiOutput"}, "example": {"code": 200, "description": null, "value": true, "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}}}, "500": {"description": "Internal error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}, "application/json": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}, "text/json": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}}}, "204": {"description": "No content response"}, "401": {"description": "User Unauthorized"}}}}, "/api/Workflow/tasks/{id}": {"get": {"tags": ["Workflow"], "summary": "Gets the workflow task by identifier.", "parameters": [{"name": "id", "in": "path", "description": "The identifier.", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns Task by Id", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/WorkflowTaskDtoApiOutput"}, "example": {"code": 200, "description": null, "value": {"id": "4f352192-8c29-456b-ab27-d6523c6fb3cd", "workflowInstanceId": "8ccf46e1-9305-4017-80ad-217c15785947", "stepId": "9c5cdeb0-60a7-4eff-a312-21f4075434b2", "userId": "<EMAIL>", "taskStatus": "FORWARDED", "taskCompleted": true, "taskActivated": true, "comments": "Forwarded using framework", "sourceId": "Project/2023", "sourceUrl": null, "workflowStatus": "IN_PROGRESS", "workflowName": "Not Billable Project Approval", "stepName": "Approval", "stepOrder": 1, "sysModifyDate": "2023-02-06T12:05:48.617+00:00", "totalRows": 0, "userAllowedToEdit": false}, "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": null}}, "application/json": {"schema": {"$ref": "#/components/schemas/WorkflowTaskDtoApiOutput"}, "example": {"code": 200, "description": null, "value": {"id": "4f352192-8c29-456b-ab27-d6523c6fb3cd", "workflowInstanceId": "8ccf46e1-9305-4017-80ad-217c15785947", "stepId": "9c5cdeb0-60a7-4eff-a312-21f4075434b2", "userId": "<EMAIL>", "taskStatus": "FORWARDED", "taskCompleted": true, "taskActivated": true, "comments": "Forwarded using framework", "sourceId": "Project/2023", "sourceUrl": null, "workflowStatus": "IN_PROGRESS", "workflowName": "Not Billable Project Approval", "stepName": "Approval", "stepOrder": 1, "sysModifyDate": "2023-02-06T12:05:48.617+00:00", "totalRows": 0, "userAllowedToEdit": false}, "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": null}}, "text/json": {"schema": {"$ref": "#/components/schemas/WorkflowTaskDtoApiOutput"}, "example": {"code": 200, "description": null, "value": {"id": "4f352192-8c29-456b-ab27-d6523c6fb3cd", "workflowInstanceId": "8ccf46e1-9305-4017-80ad-217c15785947", "stepId": "9c5cdeb0-60a7-4eff-a312-21f4075434b2", "userId": "<EMAIL>", "taskStatus": "FORWARDED", "taskCompleted": true, "taskActivated": true, "comments": "Forwarded using framework", "sourceId": "Project/2023", "sourceUrl": null, "workflowStatus": "IN_PROGRESS", "workflowName": "Not Billable Project Approval", "stepName": "Approval", "stepOrder": 1, "sysModifyDate": "2023-02-06T12:05:48.617+00:00", "totalRows": 0, "userAllowedToEdit": false}, "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": null}}}}, "400": {"description": "Malformed request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}}}, "500": {"description": "Internal error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}, "application/json": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}, "text/json": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}}}, "204": {"description": "No content response"}, "401": {"description": "User Unauthorized"}}}}, "/api/Workflow/tasks": {"get": {"tags": ["Workflow"], "summary": "Gets the list of workflow tasks.", "parameters": [{"name": "SourceType", "in": "query", "description": "Gets or sets the type of the source.", "schema": {"type": "string"}}, {"name": "UserId", "in": "query", "description": "Gets or sets the user identifier.", "schema": {"type": "string"}}, {"name": "TaskStatus", "in": "query", "description": "Gets or sets the workflow task status.", "schema": {"$ref": "#/components/schemas/TaskStatus"}}, {"name": "TaskCompleted", "in": "query", "description": "Represents an event that is raised when a task either successfully or unsuccessfully completes.", "schema": {"type": "boolean"}}, {"name": "<PERSON><PERSON><PERSON>", "in": "query", "description": "Gets or sets the name of the step.", "schema": {"type": "string"}}, {"name": "WorkflowName", "in": "query", "description": "Gets or sets the name of the workflow.", "schema": {"type": "string"}}, {"name": "WorkflowStatus", "in": "query", "description": "Gets or sets the workflow status.", "schema": {"$ref": "#/components/schemas/WorkflowStatus"}}, {"name": "SortField", "in": "query", "description": "Gets or sets the sort field.", "schema": {"type": "string"}}, {"name": "SortDirection", "in": "query", "description": "Gets or sets the sort direction.", "schema": {"type": "string"}}, {"name": "ItemsPerPage", "in": "query", "description": "Gets or sets the items per page.", "schema": {"type": "integer", "format": "int32"}}, {"name": "CurrentPage", "in": "query", "description": "Gets or sets the current page.", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Returns a list of Tasks", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/WorkflowTasksListDtoApiOutput"}, "example": {"code": 200, "description": null, "value": {"tasks": [{"id": "f80c4b6d-61ce-406a-8cd1-d66361adc977", "workflowInstanceId": "1949db44-5cd7-409c-9244-07a5210fa56f", "stepId": "53ab3926-3311-4565-b732-dbe2b8d156ac", "userId": "<EMAIL>", "taskStatus": "TODO", "taskCompleted": false, "taskActivated": true, "comments": "", "sourceId": "Project/2023", "sourceUrl": null, "workflowStatus": "IN_PROGRESS", "workflowName": "Not Billable Project Approval", "stepName": "Approval", "stepOrder": 1, "sysModifyDate": "2023-01-18T15:59:43.713+00:00", "totalRows": 0, "userAllowedToEdit": false}, {"id": "cc7876b1-7596-40d4-b113-17e2b4d202e4", "workflowInstanceId": "ed905dba-0d04-4d7e-abc3-580512a7a68f", "stepId": "bdac4a3f-3775-4582-a52c-0cdc801e8776", "userId": "<EMAIL>", "taskStatus": "TODO", "taskCompleted": false, "taskActivated": true, "comments": "", "sourceId": "959c70a9-cde6-4cd0-ad57-f6c72ae6b981/c0c23c09-0761-4b59-9f2f-5a3645d9cb57", "sourceUrl": "/dashboard/admin/projects/959c70a9-cde6-4cd0-ad57-f6c72ae6b981/detail", "workflowStatus": "IN_PROGRESS", "workflowName": "PROJECT APPROVAL", "stepName": "Step 1", "stepOrder": 0, "sysModifyDate": "2023-01-30T17:42:49.507+00:00", "totalRows": 0, "userAllowedToEdit": false}], "totalRows": 200}, "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}, "application/json": {"schema": {"$ref": "#/components/schemas/WorkflowTasksListDtoApiOutput"}, "example": {"code": 200, "description": null, "value": {"tasks": [{"id": "f80c4b6d-61ce-406a-8cd1-d66361adc977", "workflowInstanceId": "1949db44-5cd7-409c-9244-07a5210fa56f", "stepId": "53ab3926-3311-4565-b732-dbe2b8d156ac", "userId": "<EMAIL>", "taskStatus": "TODO", "taskCompleted": false, "taskActivated": true, "comments": "", "sourceId": "Project/2023", "sourceUrl": null, "workflowStatus": "IN_PROGRESS", "workflowName": "Not Billable Project Approval", "stepName": "Approval", "stepOrder": 1, "sysModifyDate": "2023-01-18T15:59:43.713+00:00", "totalRows": 0, "userAllowedToEdit": false}, {"id": "cc7876b1-7596-40d4-b113-17e2b4d202e4", "workflowInstanceId": "ed905dba-0d04-4d7e-abc3-580512a7a68f", "stepId": "bdac4a3f-3775-4582-a52c-0cdc801e8776", "userId": "<EMAIL>", "taskStatus": "TODO", "taskCompleted": false, "taskActivated": true, "comments": "", "sourceId": "959c70a9-cde6-4cd0-ad57-f6c72ae6b981/c0c23c09-0761-4b59-9f2f-5a3645d9cb57", "sourceUrl": "/dashboard/admin/projects/959c70a9-cde6-4cd0-ad57-f6c72ae6b981/detail", "workflowStatus": "IN_PROGRESS", "workflowName": "PROJECT APPROVAL", "stepName": "Step 1", "stepOrder": 0, "sysModifyDate": "2023-01-30T17:42:49.507+00:00", "totalRows": 0, "userAllowedToEdit": false}], "totalRows": 200}, "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}, "text/json": {"schema": {"$ref": "#/components/schemas/WorkflowTasksListDtoApiOutput"}, "example": {"code": 200, "description": null, "value": {"tasks": [{"id": "f80c4b6d-61ce-406a-8cd1-d66361adc977", "workflowInstanceId": "1949db44-5cd7-409c-9244-07a5210fa56f", "stepId": "53ab3926-3311-4565-b732-dbe2b8d156ac", "userId": "<EMAIL>", "taskStatus": "TODO", "taskCompleted": false, "taskActivated": true, "comments": "", "sourceId": "Project/2023", "sourceUrl": null, "workflowStatus": "IN_PROGRESS", "workflowName": "Not Billable Project Approval", "stepName": "Approval", "stepOrder": 1, "sysModifyDate": "2023-01-18T15:59:43.713+00:00", "totalRows": 0, "userAllowedToEdit": false}, {"id": "cc7876b1-7596-40d4-b113-17e2b4d202e4", "workflowInstanceId": "ed905dba-0d04-4d7e-abc3-580512a7a68f", "stepId": "bdac4a3f-3775-4582-a52c-0cdc801e8776", "userId": "<EMAIL>", "taskStatus": "TODO", "taskCompleted": false, "taskActivated": true, "comments": "", "sourceId": "959c70a9-cde6-4cd0-ad57-f6c72ae6b981/c0c23c09-0761-4b59-9f2f-5a3645d9cb57", "sourceUrl": "/dashboard/admin/projects/959c70a9-cde6-4cd0-ad57-f6c72ae6b981/detail", "workflowStatus": "IN_PROGRESS", "workflowName": "PROJECT APPROVAL", "stepName": "Step 1", "stepOrder": 0, "sysModifyDate": "2023-01-30T17:42:49.507+00:00", "totalRows": 0, "userAllowedToEdit": false}], "totalRows": 200}, "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}}}, "500": {"description": "Internal error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}, "application/json": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}, "text/json": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}}}, "204": {"description": "No content response"}, "401": {"description": "User Unauthorized"}}}}, "/api/Workflow/task/update": {"put": {"tags": ["Workflow"], "summary": "Updates the task.", "requestBody": {"description": "The request.", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/UpdateTaskDto"}, "examples": {"APPROVED - With Custom Email": {"value": {"id": "42d28bd3-42b1-4861-9e31-09e74cdc4061", "taskStatus": "APPROVED", "taskCompleted": true, "comments": "For me everything is alright i will approve", "emailRequest": {"templateId": "d-c43961e085e04f1c8e1f0192db1c69d8", "recipients": ["<EMAIL>", "<EMAIL>"], "templateData": {"Message": "This is an example of Email"}, "subject": "FrameWork Example of Email", "sendEmail": true}}}, "REJECTED - Not Sending Email": {"value": {"id": "7f77fdde-0753-4e2c-8d83-84e079ac1e07", "taskStatus": "REJECTED", "taskCompleted": true, "comments": "I don't agreed so i will reject", "emailRequest": {"templateId": "d-c43961e085e04f1c8e1f0192db1c69d8", "recipients": ["<EMAIL>", "<EMAIL>"], "templateData": {"Message": "This is an example of Email"}, "subject": "FrameWork Example of Email", "sendEmail": false}}}, "CANCELLED - With Default Email": {"value": {"id": "b13cea1e-18e6-4cf1-b94b-79fac9360c1d", "taskStatus": "CANCELLED", "taskCompleted": true, "comments": "This Task doesn't make sense now, so i will cancel it", "emailRequest": null}}}}, "application/json": {"schema": {"$ref": "#/components/schemas/UpdateTaskDto"}, "examples": {"APPROVED - With Custom Email": {"value": {"id": "42d28bd3-42b1-4861-9e31-09e74cdc4061", "taskStatus": "APPROVED", "taskCompleted": true, "comments": "For me everything is alright i will approve", "emailRequest": {"templateId": "d-c43961e085e04f1c8e1f0192db1c69d8", "recipients": ["<EMAIL>", "<EMAIL>"], "templateData": {"Message": "This is an example of Email"}, "subject": "FrameWork Example of Email", "sendEmail": true}}}, "REJECTED - Not Sending Email": {"value": {"id": "7f77fdde-0753-4e2c-8d83-84e079ac1e07", "taskStatus": "REJECTED", "taskCompleted": true, "comments": "I don't agreed so i will reject", "emailRequest": {"templateId": "d-c43961e085e04f1c8e1f0192db1c69d8", "recipients": ["<EMAIL>", "<EMAIL>"], "templateData": {"Message": "This is an example of Email"}, "subject": "FrameWork Example of Email", "sendEmail": false}}}, "CANCELLED - With Default Email": {"value": {"id": "b13cea1e-18e6-4cf1-b94b-79fac9360c1d", "taskStatus": "CANCELLED", "taskCompleted": true, "comments": "This Task doesn't make sense now, so i will cancel it", "emailRequest": null}}}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateTaskDto"}, "examples": {"APPROVED - With Custom Email": {"value": {"id": "42d28bd3-42b1-4861-9e31-09e74cdc4061", "taskStatus": "APPROVED", "taskCompleted": true, "comments": "For me everything is alright i will approve", "emailRequest": {"templateId": "d-c43961e085e04f1c8e1f0192db1c69d8", "recipients": ["<EMAIL>", "<EMAIL>"], "templateData": {"Message": "This is an example of Email"}, "subject": "FrameWork Example of Email", "sendEmail": true}}}, "REJECTED - Not Sending Email": {"value": {"id": "7f77fdde-0753-4e2c-8d83-84e079ac1e07", "taskStatus": "REJECTED", "taskCompleted": true, "comments": "I don't agreed so i will reject", "emailRequest": {"templateId": "d-c43961e085e04f1c8e1f0192db1c69d8", "recipients": ["<EMAIL>", "<EMAIL>"], "templateData": {"Message": "This is an example of Email"}, "subject": "FrameWork Example of Email", "sendEmail": false}}}, "CANCELLED - With Default Email": {"value": {"id": "b13cea1e-18e6-4cf1-b94b-79fac9360c1d", "taskStatus": "CANCELLED", "taskCompleted": true, "comments": "This Task doesn't make sense now, so i will cancel it", "emailRequest": null}}}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateTaskDto"}, "examples": {"APPROVED - With Custom Email": {"value": {"id": "42d28bd3-42b1-4861-9e31-09e74cdc4061", "taskStatus": "APPROVED", "taskCompleted": true, "comments": "For me everything is alright i will approve", "emailRequest": {"templateId": "d-c43961e085e04f1c8e1f0192db1c69d8", "recipients": ["<EMAIL>", "<EMAIL>"], "templateData": {"Message": "This is an example of Email"}, "subject": "FrameWork Example of Email", "sendEmail": true}}}, "REJECTED - Not Sending Email": {"value": {"id": "7f77fdde-0753-4e2c-8d83-84e079ac1e07", "taskStatus": "REJECTED", "taskCompleted": true, "comments": "I don't agreed so i will reject", "emailRequest": {"templateId": "d-c43961e085e04f1c8e1f0192db1c69d8", "recipients": ["<EMAIL>", "<EMAIL>"], "templateData": {"Message": "This is an example of Email"}, "subject": "FrameWork Example of Email", "sendEmail": false}}}, "CANCELLED - With Default Email": {"value": {"id": "b13cea1e-18e6-4cf1-b94b-79fac9360c1d", "taskStatus": "CANCELLED", "taskCompleted": true, "comments": "This Task doesn't make sense now, so i will cancel it", "emailRequest": null}}}}}}, "responses": {"200": {"description": "Returns True if sucess, false otherwise", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanApiOutput"}, "example": {"code": 200, "description": null, "value": true, "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanApiOutput"}, "example": {"code": 200, "description": null, "value": true, "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanApiOutput"}, "example": {"code": 200, "description": null, "value": true, "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}}}, "400": {"description": "Malformed request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}}}, "500": {"description": "Internal error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}, "application/json": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}, "text/json": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}}}, "401": {"description": "User Unauthorized"}}}}, "/api/Workflow/tasks/update": {"put": {"tags": ["Workflow"], "summary": "Updates multiple tasks.", "requestBody": {"description": "The request.", "content": {"application/json-patch+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UpdateTaskDto"}}, "examples": {"APPROVED - With Custom Email": {"value": [{"id": "c9d2ea46-c040-42c2-bef8-a69739410f58", "taskStatus": "APPROVED", "taskCompleted": true, "comments": "For me everything is alright i will approve", "emailRequest": {"templateId": "d-c43961e085e04f1c8e1f0192db1c69d8", "recipients": ["<EMAIL>", "<EMAIL>"], "templateData": {"Message": "This is an example of Email"}, "subject": "FrameWork Example of Email", "sendEmail": true}}]}, "REJECTED - Not Sending Email": {"value": [{"id": "1e9f5b2f-497b-47ad-bb3d-06773d645af5", "taskStatus": "REJECTED", "taskCompleted": true, "comments": "I don't agreed so i will reject", "emailRequest": {"templateId": "d-c43961e085e04f1c8e1f0192db1c69d8", "recipients": ["<EMAIL>", "<EMAIL>"], "templateData": {"Message": "This is an example of Email"}, "subject": "FrameWork Example of Email", "sendEmail": false}}]}, "CANCELLED - With Default Email": {"value": [{"id": "30ee96fe-02cb-46db-be0d-a47564dcb67d", "taskStatus": "CANCELLED", "taskCompleted": true, "comments": "This Task doesn't make sense now, so i will cancel it", "emailRequest": null}]}, "Multiple Tasks - APPROVED, CANCELLED": {"value": [{"id": "eecc80c9-eeb1-46e4-9202-34c6480ae152", "taskStatus": "APPROVED", "taskCompleted": true, "comments": "For me everything is alright i will approve", "emailRequest": {"templateId": "d-c43961e085e04f1c8e1f0192db1c69d8", "recipients": ["<EMAIL>", "<EMAIL>"], "templateData": {"Message": "This is an example of Email"}, "subject": "FrameWork Example of Email", "sendEmail": true}}, {"id": "4ca787d7-517d-4817-9247-103d7d8cc6aa", "taskStatus": "CANCELLED", "taskCompleted": true, "comments": "This Task doesn't make sense now, so i will cancel it", "emailRequest": null}]}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UpdateTaskDto"}}, "examples": {"APPROVED - With Custom Email": {"value": [{"id": "c9d2ea46-c040-42c2-bef8-a69739410f58", "taskStatus": "APPROVED", "taskCompleted": true, "comments": "For me everything is alright i will approve", "emailRequest": {"templateId": "d-c43961e085e04f1c8e1f0192db1c69d8", "recipients": ["<EMAIL>", "<EMAIL>"], "templateData": {"Message": "This is an example of Email"}, "subject": "FrameWork Example of Email", "sendEmail": true}}]}, "REJECTED - Not Sending Email": {"value": [{"id": "1e9f5b2f-497b-47ad-bb3d-06773d645af5", "taskStatus": "REJECTED", "taskCompleted": true, "comments": "I don't agreed so i will reject", "emailRequest": {"templateId": "d-c43961e085e04f1c8e1f0192db1c69d8", "recipients": ["<EMAIL>", "<EMAIL>"], "templateData": {"Message": "This is an example of Email"}, "subject": "FrameWork Example of Email", "sendEmail": false}}]}, "CANCELLED - With Default Email": {"value": [{"id": "30ee96fe-02cb-46db-be0d-a47564dcb67d", "taskStatus": "CANCELLED", "taskCompleted": true, "comments": "This Task doesn't make sense now, so i will cancel it", "emailRequest": null}]}, "Multiple Tasks - APPROVED, CANCELLED": {"value": [{"id": "eecc80c9-eeb1-46e4-9202-34c6480ae152", "taskStatus": "APPROVED", "taskCompleted": true, "comments": "For me everything is alright i will approve", "emailRequest": {"templateId": "d-c43961e085e04f1c8e1f0192db1c69d8", "recipients": ["<EMAIL>", "<EMAIL>"], "templateData": {"Message": "This is an example of Email"}, "subject": "FrameWork Example of Email", "sendEmail": true}}, {"id": "4ca787d7-517d-4817-9247-103d7d8cc6aa", "taskStatus": "CANCELLED", "taskCompleted": true, "comments": "This Task doesn't make sense now, so i will cancel it", "emailRequest": null}]}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UpdateTaskDto"}}, "examples": {"APPROVED - With Custom Email": {"value": [{"id": "c9d2ea46-c040-42c2-bef8-a69739410f58", "taskStatus": "APPROVED", "taskCompleted": true, "comments": "For me everything is alright i will approve", "emailRequest": {"templateId": "d-c43961e085e04f1c8e1f0192db1c69d8", "recipients": ["<EMAIL>", "<EMAIL>"], "templateData": {"Message": "This is an example of Email"}, "subject": "FrameWork Example of Email", "sendEmail": true}}]}, "REJECTED - Not Sending Email": {"value": [{"id": "1e9f5b2f-497b-47ad-bb3d-06773d645af5", "taskStatus": "REJECTED", "taskCompleted": true, "comments": "I don't agreed so i will reject", "emailRequest": {"templateId": "d-c43961e085e04f1c8e1f0192db1c69d8", "recipients": ["<EMAIL>", "<EMAIL>"], "templateData": {"Message": "This is an example of Email"}, "subject": "FrameWork Example of Email", "sendEmail": false}}]}, "CANCELLED - With Default Email": {"value": [{"id": "30ee96fe-02cb-46db-be0d-a47564dcb67d", "taskStatus": "CANCELLED", "taskCompleted": true, "comments": "This Task doesn't make sense now, so i will cancel it", "emailRequest": null}]}, "Multiple Tasks - APPROVED, CANCELLED": {"value": [{"id": "eecc80c9-eeb1-46e4-9202-34c6480ae152", "taskStatus": "APPROVED", "taskCompleted": true, "comments": "For me everything is alright i will approve", "emailRequest": {"templateId": "d-c43961e085e04f1c8e1f0192db1c69d8", "recipients": ["<EMAIL>", "<EMAIL>"], "templateData": {"Message": "This is an example of Email"}, "subject": "FrameWork Example of Email", "sendEmail": true}}, {"id": "4ca787d7-517d-4817-9247-103d7d8cc6aa", "taskStatus": "CANCELLED", "taskCompleted": true, "comments": "This Task doesn't make sense now, so i will cancel it", "emailRequest": null}]}}}, "application/*+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UpdateTaskDto"}}, "examples": {"APPROVED - With Custom Email": {"value": [{"id": "c9d2ea46-c040-42c2-bef8-a69739410f58", "taskStatus": "APPROVED", "taskCompleted": true, "comments": "For me everything is alright i will approve", "emailRequest": {"templateId": "d-c43961e085e04f1c8e1f0192db1c69d8", "recipients": ["<EMAIL>", "<EMAIL>"], "templateData": {"Message": "This is an example of Email"}, "subject": "FrameWork Example of Email", "sendEmail": true}}]}, "REJECTED - Not Sending Email": {"value": [{"id": "1e9f5b2f-497b-47ad-bb3d-06773d645af5", "taskStatus": "REJECTED", "taskCompleted": true, "comments": "I don't agreed so i will reject", "emailRequest": {"templateId": "d-c43961e085e04f1c8e1f0192db1c69d8", "recipients": ["<EMAIL>", "<EMAIL>"], "templateData": {"Message": "This is an example of Email"}, "subject": "FrameWork Example of Email", "sendEmail": false}}]}, "CANCELLED - With Default Email": {"value": [{"id": "30ee96fe-02cb-46db-be0d-a47564dcb67d", "taskStatus": "CANCELLED", "taskCompleted": true, "comments": "This Task doesn't make sense now, so i will cancel it", "emailRequest": null}]}, "Multiple Tasks - APPROVED, CANCELLED": {"value": [{"id": "eecc80c9-eeb1-46e4-9202-34c6480ae152", "taskStatus": "APPROVED", "taskCompleted": true, "comments": "For me everything is alright i will approve", "emailRequest": {"templateId": "d-c43961e085e04f1c8e1f0192db1c69d8", "recipients": ["<EMAIL>", "<EMAIL>"], "templateData": {"Message": "This is an example of Email"}, "subject": "FrameWork Example of Email", "sendEmail": true}}, {"id": "4ca787d7-517d-4817-9247-103d7d8cc6aa", "taskStatus": "CANCELLED", "taskCompleted": true, "comments": "This Task doesn't make sense now, so i will cancel it", "emailRequest": null}]}}}}}, "responses": {"200": {"description": "Returns the update output for each task", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UpdateTaskOutputDtoListApiOutput"}, "example": {"code": 200, "description": null, "value": [{"workflowStatus": "APPROVED", "workflowPurpose": "APPROVAL", "sourceType": "DOCUMENT", "taskId": "dc9c63d9-b3fa-4130-ac2b-a0c2f9e725d7", "workflowInstanceId": "f3e85368-37ff-439c-8aca-ec3d89ca8811"}, {"workflowStatus": "REJECTED", "workflowPurpose": "APPROVAL", "sourceType": "VACATION", "taskId": "0244a015-5b88-4250-8713-8d9000c16ff2", "workflowInstanceId": "8c40f8d8-f7ab-4511-898f-aa6909ffaf0c"}], "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}, "application/json": {"schema": {"$ref": "#/components/schemas/UpdateTaskOutputDtoListApiOutput"}, "example": {"code": 200, "description": null, "value": [{"workflowStatus": "APPROVED", "workflowPurpose": "APPROVAL", "sourceType": "DOCUMENT", "taskId": "dc9c63d9-b3fa-4130-ac2b-a0c2f9e725d7", "workflowInstanceId": "f3e85368-37ff-439c-8aca-ec3d89ca8811"}, {"workflowStatus": "REJECTED", "workflowPurpose": "APPROVAL", "sourceType": "VACATION", "taskId": "0244a015-5b88-4250-8713-8d9000c16ff2", "workflowInstanceId": "8c40f8d8-f7ab-4511-898f-aa6909ffaf0c"}], "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateTaskOutputDtoListApiOutput"}, "example": {"code": 200, "description": null, "value": [{"workflowStatus": "APPROVED", "workflowPurpose": "APPROVAL", "sourceType": "DOCUMENT", "taskId": "dc9c63d9-b3fa-4130-ac2b-a0c2f9e725d7", "workflowInstanceId": "f3e85368-37ff-439c-8aca-ec3d89ca8811"}, {"workflowStatus": "REJECTED", "workflowPurpose": "APPROVAL", "sourceType": "VACATION", "taskId": "0244a015-5b88-4250-8713-8d9000c16ff2", "workflowInstanceId": "8c40f8d8-f7ab-4511-898f-aa6909ffaf0c"}], "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}}}, "400": {"description": "Malformed request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}}}, "500": {"description": "Internal error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}, "application/json": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}, "text/json": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}}}, "401": {"description": "User Unauthorized"}}}}, "/api/Workflow/task/reassign": {"put": {"tags": ["Workflow"], "summary": "Reassings the task.", "requestBody": {"description": "The request.", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ReassignTaskDto"}, "examples": {"Reassign - With Custom Email": {"value": {"id": "3e7f936b-e273-4a6c-952c-35cd6d5e4e39", "newUserId": "<EMAIL>", "comments": "I don't have enough informations i will forwarded to my colleague", "emailRequest": {"templateId": "d-c43961e085e04f1c8e1f0192db1c69d8", "recipients": ["<EMAIL>", "<EMAIL>"], "templateData": {"Message": "This is an example of Email"}, "subject": "FrameWork Example of Email", "sendEmail": true}}}, "Reassign - With Default Email": {"value": {"id": "e996edfc-6d73-41a3-a898-20a06c20775c", "newUserId": "<EMAIL>", "comments": "I don't have enough informations i will forwarded to my colleague", "emailRequest": {"templateId": null, "recipients": null, "templateData": null, "subject": null, "sendEmail": true}}}, "Reassign - Not Sending Email": {"value": {"id": "6c13009e-0296-436e-b214-7a2ada5ad64e", "newUserId": "<EMAIL>", "comments": "I don't have enough informations i will forwarded to my colleague", "emailRequest": {"templateId": null, "recipients": null, "templateData": null, "subject": null, "sendEmail": false}}}}}, "application/json": {"schema": {"$ref": "#/components/schemas/ReassignTaskDto"}, "examples": {"Reassign - With Custom Email": {"value": {"id": "3e7f936b-e273-4a6c-952c-35cd6d5e4e39", "newUserId": "<EMAIL>", "comments": "I don't have enough informations i will forwarded to my colleague", "emailRequest": {"templateId": "d-c43961e085e04f1c8e1f0192db1c69d8", "recipients": ["<EMAIL>", "<EMAIL>"], "templateData": {"Message": "This is an example of Email"}, "subject": "FrameWork Example of Email", "sendEmail": true}}}, "Reassign - With Default Email": {"value": {"id": "e996edfc-6d73-41a3-a898-20a06c20775c", "newUserId": "<EMAIL>", "comments": "I don't have enough informations i will forwarded to my colleague", "emailRequest": {"templateId": null, "recipients": null, "templateData": null, "subject": null, "sendEmail": true}}}, "Reassign - Not Sending Email": {"value": {"id": "6c13009e-0296-436e-b214-7a2ada5ad64e", "newUserId": "<EMAIL>", "comments": "I don't have enough informations i will forwarded to my colleague", "emailRequest": {"templateId": null, "recipients": null, "templateData": null, "subject": null, "sendEmail": false}}}}}, "text/json": {"schema": {"$ref": "#/components/schemas/ReassignTaskDto"}, "examples": {"Reassign - With Custom Email": {"value": {"id": "3e7f936b-e273-4a6c-952c-35cd6d5e4e39", "newUserId": "<EMAIL>", "comments": "I don't have enough informations i will forwarded to my colleague", "emailRequest": {"templateId": "d-c43961e085e04f1c8e1f0192db1c69d8", "recipients": ["<EMAIL>", "<EMAIL>"], "templateData": {"Message": "This is an example of Email"}, "subject": "FrameWork Example of Email", "sendEmail": true}}}, "Reassign - With Default Email": {"value": {"id": "e996edfc-6d73-41a3-a898-20a06c20775c", "newUserId": "<EMAIL>", "comments": "I don't have enough informations i will forwarded to my colleague", "emailRequest": {"templateId": null, "recipients": null, "templateData": null, "subject": null, "sendEmail": true}}}, "Reassign - Not Sending Email": {"value": {"id": "6c13009e-0296-436e-b214-7a2ada5ad64e", "newUserId": "<EMAIL>", "comments": "I don't have enough informations i will forwarded to my colleague", "emailRequest": {"templateId": null, "recipients": null, "templateData": null, "subject": null, "sendEmail": false}}}}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ReassignTaskDto"}, "examples": {"Reassign - With Custom Email": {"value": {"id": "3e7f936b-e273-4a6c-952c-35cd6d5e4e39", "newUserId": "<EMAIL>", "comments": "I don't have enough informations i will forwarded to my colleague", "emailRequest": {"templateId": "d-c43961e085e04f1c8e1f0192db1c69d8", "recipients": ["<EMAIL>", "<EMAIL>"], "templateData": {"Message": "This is an example of Email"}, "subject": "FrameWork Example of Email", "sendEmail": true}}}, "Reassign - With Default Email": {"value": {"id": "e996edfc-6d73-41a3-a898-20a06c20775c", "newUserId": "<EMAIL>", "comments": "I don't have enough informations i will forwarded to my colleague", "emailRequest": {"templateId": null, "recipients": null, "templateData": null, "subject": null, "sendEmail": true}}}, "Reassign - Not Sending Email": {"value": {"id": "6c13009e-0296-436e-b214-7a2ada5ad64e", "newUserId": "<EMAIL>", "comments": "I don't have enough informations i will forwarded to my colleague", "emailRequest": {"templateId": null, "recipients": null, "templateData": null, "subject": null, "sendEmail": false}}}}}}}, "responses": {"200": {"description": "Returns True if sucess, false otherwise", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanApiOutput"}, "example": {"code": 200, "description": null, "value": true, "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanApiOutput"}, "example": {"code": 200, "description": null, "value": true, "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanApiOutput"}, "example": {"code": 200, "description": null, "value": true, "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}}}, "400": {"description": "Malformed request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}}}, "500": {"description": "Internal error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}, "application/json": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}, "text/json": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}}}, "401": {"description": "User Unauthorized"}}}}, "/api/Workflow/trigger/start": {"post": {"tags": ["Workflow"], "summary": "Starts the workflow.", "requestBody": {"description": "The request.", "content": {"application/json-patch+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StartWorkflowDto"}}, "examples": {"Vacation - With Custom Email": {"value": [{"displayName": "Vacation Approval", "sourceId": "Vacation/2023", "sourceUrl": "www.framework_vacation_2023.pt", "sourceType": "VACATION", "workflowType": "MANUAL", "purpose": "APPROVAL", "userId": "<EMAIL>", "autoComplete": false, "workflowStatus": null, "steps": [{"stepUsers": ["<EMAIL>", "<EMAIL>"], "requireAllToApprove": true, "skipOverOtherTasks": false, "stepName": "Phase 1", "order": 1}, {"stepUsers": ["<EMAIL>"], "requireAllToApprove": false, "skipOverOtherTasks": true, "stepName": "Phase 2", "order": 2}], "emailRequest": {"templateId": "d-c43961e085e04f1c8e1f0192db1c69d8", "recipients": ["<EMAIL>", "<EMAIL>"], "templateData": {"Message": "This is an example of Email"}, "subject": "FrameWork Example of Email", "sendEmail": true}}]}, "Project - Not Sending Email - With AutoComplete APPROVED": {"value": [{"displayName": "Project Approval", "sourceId": "Project/2023", "sourceUrl": "www.framework_Project_2023.pt", "sourceType": "PROJECT", "workflowType": "MANUAL", "purpose": "APPROVAL", "userId": "<EMAIL>", "autoComplete": true, "workflowStatus": "ACCEPTED", "steps": [{"stepUsers": ["<EMAIL>", "<EMAIL>"], "requireAllToApprove": true, "skipOverOtherTasks": false, "stepName": "Phase 1", "order": 1}, {"stepUsers": ["<EMAIL>"], "requireAllToApprove": false, "skipOverOtherTasks": true, "stepName": "Phase 2", "order": 2}], "emailRequest": {"templateId": "d-c43961e085e04f1c8e1f0192db1c69d8", "recipients": ["<EMAIL>", "<EMAIL>"], "templateData": {"Message": "This is an example of Email"}, "subject": "FrameWork Example of Email", "sendEmail": false}}]}, "Document - With Default Email - With AutoComplete REJECTED": {"value": [{"displayName": "Document Approval", "sourceId": "0037117", "sourceUrl": "www.framework_Document_2023.pt", "sourceType": "DOCUMENT", "workflowType": "MANUAL", "purpose": "APPROVAL", "userId": "<EMAIL>", "autoComplete": true, "workflowStatus": "REJECTED", "steps": [{"stepUsers": ["<EMAIL>", "<EMAIL>"], "requireAllToApprove": true, "skipOverOtherTasks": false, "stepName": "Phase 1", "order": 1}, {"stepUsers": ["<EMAIL>"], "requireAllToApprove": false, "skipOverOtherTasks": true, "stepName": "Phase 2", "order": 2}], "emailRequest": null}]}, "Timesheet - With Default Email": {"value": [{"displayName": "Timesheet Approval", "sourceId": "jmagalhaes/09-02-2023", "sourceUrl": "www.framework_Timesheet_09-02-2023.pt", "sourceType": "TIMESHEET", "workflowType": "MANUAL", "purpose": "APPROVAL", "userId": "<EMAIL>", "autoComplete": false, "workflowStatus": null, "steps": [{"stepUsers": ["<EMAIL>", "<EMAIL>"], "requireAllToApprove": true, "skipOverOtherTasks": false, "stepName": "Phase 1", "order": 1}, {"stepUsers": ["<EMAIL>"], "requireAllToApprove": false, "skipOverOtherTasks": true, "stepName": "Phase 2", "order": 2}], "emailRequest": null}]}, "Multiple Workflows - Vacation, Timesheet": {"value": [{"displayName": "Vacation Approval", "sourceId": "Vacation/2023", "sourceUrl": "www.framework_vacation_2023.pt", "sourceType": "VACATION", "workflowType": "MANUAL", "purpose": "APPROVAL", "userId": "<EMAIL>", "autoComplete": false, "workflowStatus": null, "steps": [{"stepUsers": ["<EMAIL>", "<EMAIL>"], "requireAllToApprove": true, "skipOverOtherTasks": false, "stepName": "Phase 1", "order": 1}, {"stepUsers": ["<EMAIL>"], "requireAllToApprove": false, "skipOverOtherTasks": true, "stepName": "Phase 2", "order": 2}], "emailRequest": {"templateId": "d-c43961e085e04f1c8e1f0192db1c69d8", "recipients": ["<EMAIL>", "<EMAIL>"], "templateData": {"Message": "This is an example of Email"}, "subject": "FrameWork Example of Email", "sendEmail": true}}, {"displayName": "Timesheet Approval", "sourceId": "jmagalhaes/09-02-2023", "sourceUrl": "www.framework_Timesheet_09-02-2023.pt", "sourceType": "TIMESHEET", "workflowType": "MANUAL", "purpose": "APPROVAL", "userId": "<EMAIL>", "autoComplete": false, "workflowStatus": null, "steps": [{"stepUsers": ["<EMAIL>", "<EMAIL>"], "requireAllToApprove": true, "skipOverOtherTasks": false, "stepName": "Phase 1", "order": 1}, {"stepUsers": ["<EMAIL>"], "requireAllToApprove": false, "skipOverOtherTasks": true, "stepName": "Phase 2", "order": 2}], "emailRequest": null}]}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StartWorkflowDto"}}, "examples": {"Vacation - With Custom Email": {"value": [{"displayName": "Vacation Approval", "sourceId": "Vacation/2023", "sourceUrl": "www.framework_vacation_2023.pt", "sourceType": "VACATION", "workflowType": "MANUAL", "purpose": "APPROVAL", "userId": "<EMAIL>", "autoComplete": false, "workflowStatus": null, "steps": [{"stepUsers": ["<EMAIL>", "<EMAIL>"], "requireAllToApprove": true, "skipOverOtherTasks": false, "stepName": "Phase 1", "order": 1}, {"stepUsers": ["<EMAIL>"], "requireAllToApprove": false, "skipOverOtherTasks": true, "stepName": "Phase 2", "order": 2}], "emailRequest": {"templateId": "d-c43961e085e04f1c8e1f0192db1c69d8", "recipients": ["<EMAIL>", "<EMAIL>"], "templateData": {"Message": "This is an example of Email"}, "subject": "FrameWork Example of Email", "sendEmail": true}}]}, "Project - Not Sending Email - With AutoComplete APPROVED": {"value": [{"displayName": "Project Approval", "sourceId": "Project/2023", "sourceUrl": "www.framework_Project_2023.pt", "sourceType": "PROJECT", "workflowType": "MANUAL", "purpose": "APPROVAL", "userId": "<EMAIL>", "autoComplete": true, "workflowStatus": "ACCEPTED", "steps": [{"stepUsers": ["<EMAIL>", "<EMAIL>"], "requireAllToApprove": true, "skipOverOtherTasks": false, "stepName": "Phase 1", "order": 1}, {"stepUsers": ["<EMAIL>"], "requireAllToApprove": false, "skipOverOtherTasks": true, "stepName": "Phase 2", "order": 2}], "emailRequest": {"templateId": "d-c43961e085e04f1c8e1f0192db1c69d8", "recipients": ["<EMAIL>", "<EMAIL>"], "templateData": {"Message": "This is an example of Email"}, "subject": "FrameWork Example of Email", "sendEmail": false}}]}, "Document - With Default Email - With AutoComplete REJECTED": {"value": [{"displayName": "Document Approval", "sourceId": "0037117", "sourceUrl": "www.framework_Document_2023.pt", "sourceType": "DOCUMENT", "workflowType": "MANUAL", "purpose": "APPROVAL", "userId": "<EMAIL>", "autoComplete": true, "workflowStatus": "REJECTED", "steps": [{"stepUsers": ["<EMAIL>", "<EMAIL>"], "requireAllToApprove": true, "skipOverOtherTasks": false, "stepName": "Phase 1", "order": 1}, {"stepUsers": ["<EMAIL>"], "requireAllToApprove": false, "skipOverOtherTasks": true, "stepName": "Phase 2", "order": 2}], "emailRequest": null}]}, "Timesheet - With Default Email": {"value": [{"displayName": "Timesheet Approval", "sourceId": "jmagalhaes/09-02-2023", "sourceUrl": "www.framework_Timesheet_09-02-2023.pt", "sourceType": "TIMESHEET", "workflowType": "MANUAL", "purpose": "APPROVAL", "userId": "<EMAIL>", "autoComplete": false, "workflowStatus": null, "steps": [{"stepUsers": ["<EMAIL>", "<EMAIL>"], "requireAllToApprove": true, "skipOverOtherTasks": false, "stepName": "Phase 1", "order": 1}, {"stepUsers": ["<EMAIL>"], "requireAllToApprove": false, "skipOverOtherTasks": true, "stepName": "Phase 2", "order": 2}], "emailRequest": null}]}, "Multiple Workflows - Vacation, Timesheet": {"value": [{"displayName": "Vacation Approval", "sourceId": "Vacation/2023", "sourceUrl": "www.framework_vacation_2023.pt", "sourceType": "VACATION", "workflowType": "MANUAL", "purpose": "APPROVAL", "userId": "<EMAIL>", "autoComplete": false, "workflowStatus": null, "steps": [{"stepUsers": ["<EMAIL>", "<EMAIL>"], "requireAllToApprove": true, "skipOverOtherTasks": false, "stepName": "Phase 1", "order": 1}, {"stepUsers": ["<EMAIL>"], "requireAllToApprove": false, "skipOverOtherTasks": true, "stepName": "Phase 2", "order": 2}], "emailRequest": {"templateId": "d-c43961e085e04f1c8e1f0192db1c69d8", "recipients": ["<EMAIL>", "<EMAIL>"], "templateData": {"Message": "This is an example of Email"}, "subject": "FrameWork Example of Email", "sendEmail": true}}, {"displayName": "Timesheet Approval", "sourceId": "jmagalhaes/09-02-2023", "sourceUrl": "www.framework_Timesheet_09-02-2023.pt", "sourceType": "TIMESHEET", "workflowType": "MANUAL", "purpose": "APPROVAL", "userId": "<EMAIL>", "autoComplete": false, "workflowStatus": null, "steps": [{"stepUsers": ["<EMAIL>", "<EMAIL>"], "requireAllToApprove": true, "skipOverOtherTasks": false, "stepName": "Phase 1", "order": 1}, {"stepUsers": ["<EMAIL>"], "requireAllToApprove": false, "skipOverOtherTasks": true, "stepName": "Phase 2", "order": 2}], "emailRequest": null}]}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StartWorkflowDto"}}, "examples": {"Vacation - With Custom Email": {"value": [{"displayName": "Vacation Approval", "sourceId": "Vacation/2023", "sourceUrl": "www.framework_vacation_2023.pt", "sourceType": "VACATION", "workflowType": "MANUAL", "purpose": "APPROVAL", "userId": "<EMAIL>", "autoComplete": false, "workflowStatus": null, "steps": [{"stepUsers": ["<EMAIL>", "<EMAIL>"], "requireAllToApprove": true, "skipOverOtherTasks": false, "stepName": "Phase 1", "order": 1}, {"stepUsers": ["<EMAIL>"], "requireAllToApprove": false, "skipOverOtherTasks": true, "stepName": "Phase 2", "order": 2}], "emailRequest": {"templateId": "d-c43961e085e04f1c8e1f0192db1c69d8", "recipients": ["<EMAIL>", "<EMAIL>"], "templateData": {"Message": "This is an example of Email"}, "subject": "FrameWork Example of Email", "sendEmail": true}}]}, "Project - Not Sending Email - With AutoComplete APPROVED": {"value": [{"displayName": "Project Approval", "sourceId": "Project/2023", "sourceUrl": "www.framework_Project_2023.pt", "sourceType": "PROJECT", "workflowType": "MANUAL", "purpose": "APPROVAL", "userId": "<EMAIL>", "autoComplete": true, "workflowStatus": "ACCEPTED", "steps": [{"stepUsers": ["<EMAIL>", "<EMAIL>"], "requireAllToApprove": true, "skipOverOtherTasks": false, "stepName": "Phase 1", "order": 1}, {"stepUsers": ["<EMAIL>"], "requireAllToApprove": false, "skipOverOtherTasks": true, "stepName": "Phase 2", "order": 2}], "emailRequest": {"templateId": "d-c43961e085e04f1c8e1f0192db1c69d8", "recipients": ["<EMAIL>", "<EMAIL>"], "templateData": {"Message": "This is an example of Email"}, "subject": "FrameWork Example of Email", "sendEmail": false}}]}, "Document - With Default Email - With AutoComplete REJECTED": {"value": [{"displayName": "Document Approval", "sourceId": "0037117", "sourceUrl": "www.framework_Document_2023.pt", "sourceType": "DOCUMENT", "workflowType": "MANUAL", "purpose": "APPROVAL", "userId": "<EMAIL>", "autoComplete": true, "workflowStatus": "REJECTED", "steps": [{"stepUsers": ["<EMAIL>", "<EMAIL>"], "requireAllToApprove": true, "skipOverOtherTasks": false, "stepName": "Phase 1", "order": 1}, {"stepUsers": ["<EMAIL>"], "requireAllToApprove": false, "skipOverOtherTasks": true, "stepName": "Phase 2", "order": 2}], "emailRequest": null}]}, "Timesheet - With Default Email": {"value": [{"displayName": "Timesheet Approval", "sourceId": "jmagalhaes/09-02-2023", "sourceUrl": "www.framework_Timesheet_09-02-2023.pt", "sourceType": "TIMESHEET", "workflowType": "MANUAL", "purpose": "APPROVAL", "userId": "<EMAIL>", "autoComplete": false, "workflowStatus": null, "steps": [{"stepUsers": ["<EMAIL>", "<EMAIL>"], "requireAllToApprove": true, "skipOverOtherTasks": false, "stepName": "Phase 1", "order": 1}, {"stepUsers": ["<EMAIL>"], "requireAllToApprove": false, "skipOverOtherTasks": true, "stepName": "Phase 2", "order": 2}], "emailRequest": null}]}, "Multiple Workflows - Vacation, Timesheet": {"value": [{"displayName": "Vacation Approval", "sourceId": "Vacation/2023", "sourceUrl": "www.framework_vacation_2023.pt", "sourceType": "VACATION", "workflowType": "MANUAL", "purpose": "APPROVAL", "userId": "<EMAIL>", "autoComplete": false, "workflowStatus": null, "steps": [{"stepUsers": ["<EMAIL>", "<EMAIL>"], "requireAllToApprove": true, "skipOverOtherTasks": false, "stepName": "Phase 1", "order": 1}, {"stepUsers": ["<EMAIL>"], "requireAllToApprove": false, "skipOverOtherTasks": true, "stepName": "Phase 2", "order": 2}], "emailRequest": {"templateId": "d-c43961e085e04f1c8e1f0192db1c69d8", "recipients": ["<EMAIL>", "<EMAIL>"], "templateData": {"Message": "This is an example of Email"}, "subject": "FrameWork Example of Email", "sendEmail": true}}, {"displayName": "Timesheet Approval", "sourceId": "jmagalhaes/09-02-2023", "sourceUrl": "www.framework_Timesheet_09-02-2023.pt", "sourceType": "TIMESHEET", "workflowType": "MANUAL", "purpose": "APPROVAL", "userId": "<EMAIL>", "autoComplete": false, "workflowStatus": null, "steps": [{"stepUsers": ["<EMAIL>", "<EMAIL>"], "requireAllToApprove": true, "skipOverOtherTasks": false, "stepName": "Phase 1", "order": 1}, {"stepUsers": ["<EMAIL>"], "requireAllToApprove": false, "skipOverOtherTasks": true, "stepName": "Phase 2", "order": 2}], "emailRequest": null}]}}}, "application/*+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StartWorkflowDto"}}, "examples": {"Vacation - With Custom Email": {"value": [{"displayName": "Vacation Approval", "sourceId": "Vacation/2023", "sourceUrl": "www.framework_vacation_2023.pt", "sourceType": "VACATION", "workflowType": "MANUAL", "purpose": "APPROVAL", "userId": "<EMAIL>", "autoComplete": false, "workflowStatus": null, "steps": [{"stepUsers": ["<EMAIL>", "<EMAIL>"], "requireAllToApprove": true, "skipOverOtherTasks": false, "stepName": "Phase 1", "order": 1}, {"stepUsers": ["<EMAIL>"], "requireAllToApprove": false, "skipOverOtherTasks": true, "stepName": "Phase 2", "order": 2}], "emailRequest": {"templateId": "d-c43961e085e04f1c8e1f0192db1c69d8", "recipients": ["<EMAIL>", "<EMAIL>"], "templateData": {"Message": "This is an example of Email"}, "subject": "FrameWork Example of Email", "sendEmail": true}}]}, "Project - Not Sending Email - With AutoComplete APPROVED": {"value": [{"displayName": "Project Approval", "sourceId": "Project/2023", "sourceUrl": "www.framework_Project_2023.pt", "sourceType": "PROJECT", "workflowType": "MANUAL", "purpose": "APPROVAL", "userId": "<EMAIL>", "autoComplete": true, "workflowStatus": "ACCEPTED", "steps": [{"stepUsers": ["<EMAIL>", "<EMAIL>"], "requireAllToApprove": true, "skipOverOtherTasks": false, "stepName": "Phase 1", "order": 1}, {"stepUsers": ["<EMAIL>"], "requireAllToApprove": false, "skipOverOtherTasks": true, "stepName": "Phase 2", "order": 2}], "emailRequest": {"templateId": "d-c43961e085e04f1c8e1f0192db1c69d8", "recipients": ["<EMAIL>", "<EMAIL>"], "templateData": {"Message": "This is an example of Email"}, "subject": "FrameWork Example of Email", "sendEmail": false}}]}, "Document - With Default Email - With AutoComplete REJECTED": {"value": [{"displayName": "Document Approval", "sourceId": "0037117", "sourceUrl": "www.framework_Document_2023.pt", "sourceType": "DOCUMENT", "workflowType": "MANUAL", "purpose": "APPROVAL", "userId": "<EMAIL>", "autoComplete": true, "workflowStatus": "REJECTED", "steps": [{"stepUsers": ["<EMAIL>", "<EMAIL>"], "requireAllToApprove": true, "skipOverOtherTasks": false, "stepName": "Phase 1", "order": 1}, {"stepUsers": ["<EMAIL>"], "requireAllToApprove": false, "skipOverOtherTasks": true, "stepName": "Phase 2", "order": 2}], "emailRequest": null}]}, "Timesheet - With Default Email": {"value": [{"displayName": "Timesheet Approval", "sourceId": "jmagalhaes/09-02-2023", "sourceUrl": "www.framework_Timesheet_09-02-2023.pt", "sourceType": "TIMESHEET", "workflowType": "MANUAL", "purpose": "APPROVAL", "userId": "<EMAIL>", "autoComplete": false, "workflowStatus": null, "steps": [{"stepUsers": ["<EMAIL>", "<EMAIL>"], "requireAllToApprove": true, "skipOverOtherTasks": false, "stepName": "Phase 1", "order": 1}, {"stepUsers": ["<EMAIL>"], "requireAllToApprove": false, "skipOverOtherTasks": true, "stepName": "Phase 2", "order": 2}], "emailRequest": null}]}, "Multiple Workflows - Vacation, Timesheet": {"value": [{"displayName": "Vacation Approval", "sourceId": "Vacation/2023", "sourceUrl": "www.framework_vacation_2023.pt", "sourceType": "VACATION", "workflowType": "MANUAL", "purpose": "APPROVAL", "userId": "<EMAIL>", "autoComplete": false, "workflowStatus": null, "steps": [{"stepUsers": ["<EMAIL>", "<EMAIL>"], "requireAllToApprove": true, "skipOverOtherTasks": false, "stepName": "Phase 1", "order": 1}, {"stepUsers": ["<EMAIL>"], "requireAllToApprove": false, "skipOverOtherTasks": true, "stepName": "Phase 2", "order": 2}], "emailRequest": {"templateId": "d-c43961e085e04f1c8e1f0192db1c69d8", "recipients": ["<EMAIL>", "<EMAIL>"], "templateData": {"Message": "This is an example of Email"}, "subject": "FrameWork Example of Email", "sendEmail": true}}, {"displayName": "Timesheet Approval", "sourceId": "jmagalhaes/09-02-2023", "sourceUrl": "www.framework_Timesheet_09-02-2023.pt", "sourceType": "TIMESHEET", "workflowType": "MANUAL", "purpose": "APPROVAL", "userId": "<EMAIL>", "autoComplete": false, "workflowStatus": null, "steps": [{"stepUsers": ["<EMAIL>", "<EMAIL>"], "requireAllToApprove": true, "skipOverOtherTasks": false, "stepName": "Phase 1", "order": 1}, {"stepUsers": ["<EMAIL>"], "requireAllToApprove": false, "skipOverOtherTasks": true, "stepName": "Phase 2", "order": 2}], "emailRequest": null}]}}}}}, "responses": {"200": {"description": "Returns the workflow that was started", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/WorkflowInstanceDtoApiOutput"}, "example": {"code": 200, "description": null, "value": {"id": "92b25840-1e4e-4f16-9a58-8dde276ba78f", "workflowId": "00000000-0000-0000-0000-000000000000", "displayName": "Vacation Approval", "sourceId": "Vacation/2023", "sourceUrl": "www.framework_vacation_2023.pt", "workflowStatus": "IN_PROGRESS", "sourceType": "VACATION", "workflowType": "MANUAL", "purpose": "APPROVAL", "stateProcessingRequired": false, "activeStepName": null, "steps": null, "sysCreateUserId": "<EMAIL>", "sysModifyDate": "2023-02-09T17:58:23.907+00:00"}, "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}, "application/json": {"schema": {"$ref": "#/components/schemas/WorkflowInstanceDtoApiOutput"}, "example": {"code": 200, "description": null, "value": {"id": "92b25840-1e4e-4f16-9a58-8dde276ba78f", "workflowId": "00000000-0000-0000-0000-000000000000", "displayName": "Vacation Approval", "sourceId": "Vacation/2023", "sourceUrl": "www.framework_vacation_2023.pt", "workflowStatus": "IN_PROGRESS", "sourceType": "VACATION", "workflowType": "MANUAL", "purpose": "APPROVAL", "stateProcessingRequired": false, "activeStepName": null, "steps": null, "sysCreateUserId": "<EMAIL>", "sysModifyDate": "2023-02-09T17:58:23.907+00:00"}, "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}, "text/json": {"schema": {"$ref": "#/components/schemas/WorkflowInstanceDtoApiOutput"}, "example": {"code": 200, "description": null, "value": {"id": "92b25840-1e4e-4f16-9a58-8dde276ba78f", "workflowId": "00000000-0000-0000-0000-000000000000", "displayName": "Vacation Approval", "sourceId": "Vacation/2023", "sourceUrl": "www.framework_vacation_2023.pt", "workflowStatus": "IN_PROGRESS", "sourceType": "VACATION", "workflowType": "MANUAL", "purpose": "APPROVAL", "stateProcessingRequired": false, "activeStepName": null, "steps": null, "sysCreateUserId": "<EMAIL>", "sysModifyDate": "2023-02-09T17:58:23.907+00:00"}, "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}}}, "400": {"description": "Malformed request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}}}, "500": {"description": "Internal error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}, "application/json": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}, "text/json": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}}}, "401": {"description": "User Unauthorized"}}}}, "/api/Workflow/trigger/automatic": {"post": {"tags": ["Workflow"], "summary": "Triggers the automatic workflow.", "requestBody": {"description": "The request.", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/StartAutomaticWorkflowDto"}, "examples": {"Vacation - With Custom Email": {"value": {"workflowIds": null, "sourceId": "Vacation/2023", "sourceUrl": "www.framework_vacation_2023.pt", "sourceMetadata": {"VacationFlag": "true"}, "emailRequest": {"templateId": "d-c43961e085e04f1c8e1f0192db1c69d8", "recipients": ["<EMAIL>", "<EMAIL>"], "templateData": {"Message": "This is an example of Email"}, "subject": "FrameWork Example of Email", "sendEmail": true}}}, "Project - With Default Email": {"value": {"workflowIds": ["8374a0bf-2df0-441e-9c18-a1cc4e13e5ba", "74ba0ecf-b160-480e-9a72-fb9aa0fc8e70"], "sourceId": "Project/2023", "sourceUrl": "www.framework_project_2023.pt", "sourceMetadata": {"DirectionType": "1995"}, "emailRequest": null}}}}, "application/json": {"schema": {"$ref": "#/components/schemas/StartAutomaticWorkflowDto"}, "examples": {"Vacation - With Custom Email": {"value": {"workflowIds": null, "sourceId": "Vacation/2023", "sourceUrl": "www.framework_vacation_2023.pt", "sourceMetadata": {"VacationFlag": "true"}, "emailRequest": {"templateId": "d-c43961e085e04f1c8e1f0192db1c69d8", "recipients": ["<EMAIL>", "<EMAIL>"], "templateData": {"Message": "This is an example of Email"}, "subject": "FrameWork Example of Email", "sendEmail": true}}}, "Project - With Default Email": {"value": {"workflowIds": ["8374a0bf-2df0-441e-9c18-a1cc4e13e5ba", "74ba0ecf-b160-480e-9a72-fb9aa0fc8e70"], "sourceId": "Project/2023", "sourceUrl": "www.framework_project_2023.pt", "sourceMetadata": {"DirectionType": "1995"}, "emailRequest": null}}}}, "text/json": {"schema": {"$ref": "#/components/schemas/StartAutomaticWorkflowDto"}, "examples": {"Vacation - With Custom Email": {"value": {"workflowIds": null, "sourceId": "Vacation/2023", "sourceUrl": "www.framework_vacation_2023.pt", "sourceMetadata": {"VacationFlag": "true"}, "emailRequest": {"templateId": "d-c43961e085e04f1c8e1f0192db1c69d8", "recipients": ["<EMAIL>", "<EMAIL>"], "templateData": {"Message": "This is an example of Email"}, "subject": "FrameWork Example of Email", "sendEmail": true}}}, "Project - With Default Email": {"value": {"workflowIds": ["8374a0bf-2df0-441e-9c18-a1cc4e13e5ba", "74ba0ecf-b160-480e-9a72-fb9aa0fc8e70"], "sourceId": "Project/2023", "sourceUrl": "www.framework_project_2023.pt", "sourceMetadata": {"DirectionType": "1995"}, "emailRequest": null}}}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StartAutomaticWorkflowDto"}, "examples": {"Vacation - With Custom Email": {"value": {"workflowIds": null, "sourceId": "Vacation/2023", "sourceUrl": "www.framework_vacation_2023.pt", "sourceMetadata": {"VacationFlag": "true"}, "emailRequest": {"templateId": "d-c43961e085e04f1c8e1f0192db1c69d8", "recipients": ["<EMAIL>", "<EMAIL>"], "templateData": {"Message": "This is an example of Email"}, "subject": "FrameWork Example of Email", "sendEmail": true}}}, "Project - With Default Email": {"value": {"workflowIds": ["8374a0bf-2df0-441e-9c18-a1cc4e13e5ba", "74ba0ecf-b160-480e-9a72-fb9aa0fc8e70"], "sourceId": "Project/2023", "sourceUrl": "www.framework_project_2023.pt", "sourceMetadata": {"DirectionType": "1995"}, "emailRequest": null}}}}}}, "responses": {"200": {"description": "Returns the workflow that was Automatic started", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/WorkflowInstanceDtoApiOutput"}, "example": {"code": 200, "description": null, "value": {"id": "92b25840-1e4e-4f16-9a58-8dde276ba78f", "workflowId": "00000000-0000-0000-0000-000000000000", "displayName": "Vacation Approval", "sourceId": "Vacation/2023", "sourceUrl": "www.framework_vacation_2023.pt", "workflowStatus": "IN_PROGRESS", "sourceType": "VACATION", "workflowType": "MANUAL", "purpose": "APPROVAL", "stateProcessingRequired": false, "activeStepName": null, "steps": null, "sysCreateUserId": "<EMAIL>", "sysModifyDate": "2023-02-09T17:58:23.907+00:00"}, "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}, "application/json": {"schema": {"$ref": "#/components/schemas/WorkflowInstanceDtoApiOutput"}, "example": {"code": 200, "description": null, "value": {"id": "92b25840-1e4e-4f16-9a58-8dde276ba78f", "workflowId": "00000000-0000-0000-0000-000000000000", "displayName": "Vacation Approval", "sourceId": "Vacation/2023", "sourceUrl": "www.framework_vacation_2023.pt", "workflowStatus": "IN_PROGRESS", "sourceType": "VACATION", "workflowType": "MANUAL", "purpose": "APPROVAL", "stateProcessingRequired": false, "activeStepName": null, "steps": null, "sysCreateUserId": "<EMAIL>", "sysModifyDate": "2023-02-09T17:58:23.907+00:00"}, "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}, "text/json": {"schema": {"$ref": "#/components/schemas/WorkflowInstanceDtoApiOutput"}, "example": {"code": 200, "description": null, "value": {"id": "92b25840-1e4e-4f16-9a58-8dde276ba78f", "workflowId": "00000000-0000-0000-0000-000000000000", "displayName": "Vacation Approval", "sourceId": "Vacation/2023", "sourceUrl": "www.framework_vacation_2023.pt", "workflowStatus": "IN_PROGRESS", "sourceType": "VACATION", "workflowType": "MANUAL", "purpose": "APPROVAL", "stateProcessingRequired": false, "activeStepName": null, "steps": null, "sysCreateUserId": "<EMAIL>", "sysModifyDate": "2023-02-09T17:58:23.907+00:00"}, "error": false, "exceptionMessages": {"messages": [], "hasMessages": false}, "properties": {}}}}}, "400": {"description": "Malformed request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectApiOutput"}, "example": {"code": 400, "description": "Bad Request", "value": null, "error": true, "exceptionMessages": {"messages": ["Error message 1", "Error message 2"], "hasMessages": true}, "properties": {}}}}}, "500": {"description": "Internal error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}, "application/json": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}, "text/json": {"schema": {"$ref": "#/components/schemas/ServiceOutput"}, "example": {"description": "Internal error description", "error": true}}}}, "401": {"description": "User Unauthorized"}}}}}, "components": {"schemas": {"AddAzureUsersRequest": {"type": "object", "properties": {"users": {"type": "array", "items": {"$ref": "#/components/schemas/AzureUserInfo"}, "nullable": true}, "isAdmin": {"type": "boolean"}}, "additionalProperties": false}, "ArquivoProcessado": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "nome": {"type": "string", "nullable": true}, "blobUrl": {"type": "string", "nullable": true}, "dataUpload": {"type": "string", "format": "date-time"}, "dataProcessamento": {"type": "string", "format": "date-time"}, "processado": {"type": "boolean"}, "registrosProcessados": {"type": "integer", "format": "int32"}, "registrosComErro": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "AzureUserInfo": {"type": "object", "properties": {"azureId": {"type": "string", "nullable": true}, "displayName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BooleanApiOutput": {"type": "object", "properties": {"code": {"$ref": "#/components/schemas/HttpStatusCode"}, "description": {"type": "string", "description": "Gets or sets the description.", "nullable": true}, "value": {"type": "boolean", "description": "Gets or sets the value."}, "error": {"type": "boolean", "description": "Gets or sets a value indicating whether this RnD.BackEnd.API.Model.Generic.ApiOutput`1 is error."}, "exceptionMessages": {"$ref": "#/components/schemas/ModelException"}, "properties": {"type": "object", "additionalProperties": {"nullable": true}, "description": "Gets or sets the properties.", "nullable": true}}, "additionalProperties": false, "description": "API output class"}, "Cliente": {"required": ["contact"], "type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "contact": {"minLength": 1, "type": "string"}, "consumo_Cliente": {"type": "array", "items": {"$ref": "#/components/schemas/Consumo_Cliente"}, "nullable": true}}, "additionalProperties": false}, "ClienteSubscricao": {"required": ["clienteID", "subscriptionID"], "type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "clienteID": {"type": "integer", "format": "int32"}, "subscriptionID": {"type": "integer", "format": "int32"}, "cliente": {"$ref": "#/components/schemas/Cliente"}, "subscription": {"$ref": "#/components/schemas/Subscription"}}, "additionalProperties": false}, "ClienteSubscricaoInputModel": {"type": "object", "properties": {"clienteID": {"type": "integer", "format": "int32"}, "subscriptionID": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "CodeAcademyTemplateDto": {"type": "object", "properties": {"message": {"type": "string", "description": "Gets or sets the message.", "nullable": true}, "subject": {"type": "string", "description": "Gets or sets the subject.", "nullable": true}, "session": {"type": "string", "description": "Gets or sets the session.", "nullable": true}, "date": {"type": "string", "description": "Gets or sets the date.", "nullable": true}}, "additionalProperties": false, "description": "Code academy email template DTO class"}, "CodeAcademyTemplateDtoEmailMessageDto": {"type": "object", "properties": {"templateData": {"$ref": "#/components/schemas/CodeAcademyTemplateDto"}, "recipients": {"type": "array", "items": {"type": "string"}, "description": "Gets or sets the recipients.", "nullable": true}, "recipientsCC": {"type": "array", "items": {"type": "string"}, "description": "Gets or sets the recipients cc.", "nullable": true}, "recipientsBCC": {"type": "array", "items": {"type": "string"}, "description": "Gets or sets the recipients BCC.", "nullable": true}}, "additionalProperties": false, "description": "Email message DTO class"}, "Consumo": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "clienteID": {"type": "integer", "format": "int32"}, "dataInicio": {"type": "string", "format": "date-time"}, "dataFim": {"type": "string", "format": "date-time"}, "countryListTotal": {"type": "number", "format": "double"}, "countryResellerTotal": {"type": "number", "format": "double"}, "countryCustomerTotal": {"type": "number", "format": "double"}, "tipoServico": {"type": "string", "nullable": true}, "regiao": {"type": "string", "nullable": true}, "moeda": {"type": "string", "nullable": true}, "consumo_Cliente": {"type": "array", "items": {"$ref": "#/components/schemas/Consumo_Cliente"}, "nullable": true}}, "additionalProperties": false}, "ConsumoPDF": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "clienteID": {"type": "integer", "format": "int32"}, "dataInicio": {"type": "string", "format": "date-time"}, "dataFim": {"type": "string", "format": "date-time"}, "total": {"type": "number", "format": "double"}, "iva": {"type": "number", "format": "double"}, "totalFatura": {"type": "number", "format": "double"}, "codigo": {"type": "string", "nullable": true}, "identificador": {"type": "string", "nullable": true}, "cliente": {"$ref": "#/components/schemas/Cliente"}}, "additionalProperties": false}, "ConsumoSubscricao": {"required": ["cost", "costUSD", "currency", "endDate", "resourceGroup", "resourceLocation", "resourceName", "startDate", "subscriptionID"], "type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "subscriptionID": {"maxLength": 255, "minLength": 1, "type": "string"}, "resourceGroup": {"maxLength": 255, "minLength": 1, "type": "string"}, "resourceName": {"maxLength": 255, "minLength": 1, "type": "string"}, "resourceLocation": {"maxLength": 255, "minLength": 1, "type": "string"}, "cost": {"type": "number", "format": "double"}, "costUSD": {"type": "number", "format": "double"}, "currency": {"maxLength": 3, "minLength": 1, "type": "string"}, "startDate": {"type": "string", "format": "date-time"}, "endDate": {"type": "string", "format": "date-time"}, "subscription": {"$ref": "#/components/schemas/Subscription"}}, "additionalProperties": false}, "ConsumoSubscricaoExcelModel": {"type": "object", "properties": {"subscriptionId": {"type": "string", "nullable": true}, "resourceGroup": {"type": "string", "nullable": true}, "resourceName": {"type": "string", "nullable": true}, "resourceLocation": {"type": "string", "nullable": true}, "cost": {"type": "number", "format": "double"}, "costUSD": {"type": "number", "format": "double"}, "currency": {"type": "string", "nullable": true}, "startDate": {"type": "string", "format": "date-time"}, "endDate": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "Consumo_Cliente": {"required": ["clienteID", "consumoID"], "type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "clienteID": {"type": "integer", "format": "int32"}, "cliente": {"$ref": "#/components/schemas/Cliente"}, "consumoID": {"type": "integer", "format": "int32"}, "consumo": {"$ref": "#/components/schemas/Consumo"}}, "additionalProperties": false}, "CreateVehicleDto": {"type": "object", "properties": {"brandId": {"type": "string", "description": "Gets or sets the brand identifier.", "nullable": true}, "brand": {"type": "string", "description": "Gets or sets the brand.", "nullable": true}, "modelName": {"type": "string", "description": "Gets or sets the name of the model.", "nullable": true}, "fuelType": {"type": "string", "description": "Gets or sets the type of the fuel.", "nullable": true}, "version": {"type": "string", "description": "Gets or sets the version.", "nullable": true}, "year": {"type": "integer", "description": "Gets or sets the year.", "format": "int32"}}, "additionalProperties": false, "description": "Create vehicle DTO class"}, "CreateWeatherDto": {"type": "object", "properties": {"temperature": {"type": "integer", "description": "Gets or sets the temperature.", "format": "int32"}, "summary": {"type": "string", "description": "Gets or sets the summary.", "nullable": true}, "location": {"type": "string", "description": "Gets or sets the location.", "nullable": true}}, "additionalProperties": false, "description": "Create weather DTO class"}, "CustomStepDto": {"type": "object", "properties": {"stepUsers": {"type": "array", "items": {"type": "string"}, "description": "Gets or sets the step users.", "nullable": true}, "requireAllToApprove": {"type": "boolean", "description": "Gets or sets a value indicating whether [require all to approve]."}, "skipOverOtherTasks": {"type": "boolean", "description": "Gets or sets a value indicating whether [skip over other tasks]."}, "stepName": {"type": "string", "description": "Gets or sets the name of the step.", "nullable": true}, "order": {"type": "integer", "description": "Gets or sets the order.", "format": "int32"}}, "additionalProperties": false, "description": "Custom step request DTO class"}, "EmailDto": {"type": "object", "properties": {"message": {"type": "string", "description": "Gets or sets the message.", "nullable": true}, "subject": {"type": "string", "description": "Gets or sets the subject.", "nullable": true}}, "additionalProperties": false, "description": "Email DTO class"}, "EmailDtoEmailMessageDto": {"type": "object", "properties": {"templateData": {"$ref": "#/components/schemas/EmailDto"}, "recipients": {"type": "array", "items": {"type": "string"}, "description": "Gets or sets the recipients.", "nullable": true}, "recipientsCC": {"type": "array", "items": {"type": "string"}, "description": "Gets or sets the recipients cc.", "nullable": true}, "recipientsBCC": {"type": "array", "items": {"type": "string"}, "description": "Gets or sets the recipients BCC.", "nullable": true}}, "additionalProperties": false, "description": "Email message DTO class"}, "EntityDto": {"type": "object", "properties": {"id": {"type": "string", "description": "Gets or sets the identifier.", "nullable": true}, "modelId": {"type": "string", "description": "Gets or sets the model identifier.", "nullable": true}, "modelName": {"type": "string", "description": "Gets or sets the name of the model.", "nullable": true}, "functionalName": {"type": "string", "description": "Gets or sets the name of the functional.", "nullable": true}, "technicalName": {"type": "string", "description": "Gets or sets the name of the technical.", "nullable": true}, "dataLength": {"type": "integer", "description": "Gets or sets the length of the data.", "format": "int32"}, "created": {"type": "string", "description": "Gets or sets the created.", "format": "date-time"}, "createdBy": {"type": "string", "description": "Gets or sets the created by.", "nullable": true}, "createdByName": {"type": "string", "description": "Gets or sets the name of the created by.", "nullable": true}, "modified": {"type": "string", "description": "Gets or sets the modified.", "format": "date-time"}, "modifiedBy": {"type": "string", "description": "Gets or sets the modified by.", "nullable": true}, "modifiedByName": {"type": "string", "description": "Gets or sets the name of the modified by.", "nullable": true}}, "additionalProperties": false, "description": "Data Entry entity DTO class"}, "EntityListDto": {"type": "object", "properties": {"entities": {"type": "array", "items": {"$ref": "#/components/schemas/EntityDto"}, "description": "Gets or sets the entities.", "nullable": true}, "totalRows": {"type": "integer", "description": "Gets or sets the total rows.", "format": "int32"}}, "additionalProperties": false, "description": "Data Entry entity list DTO class"}, "EntityListDtoApiOutput": {"type": "object", "properties": {"code": {"$ref": "#/components/schemas/HttpStatusCode"}, "description": {"type": "string", "description": "Gets or sets the description.", "nullable": true}, "value": {"$ref": "#/components/schemas/EntityListDto"}, "error": {"type": "boolean", "description": "Gets or sets a value indicating whether this RnD.BackEnd.API.Model.Generic.ApiOutput`1 is error."}, "exceptionMessages": {"$ref": "#/components/schemas/ModelException"}, "properties": {"type": "object", "additionalProperties": {"nullable": true}, "description": "Gets or sets the properties.", "nullable": true}}, "additionalProperties": false, "description": "API output class"}, "HttpStatusCode": {"enum": ["Continue", "SwitchingProtocols", "Processing", "EarlyHints", "OK", "Created", "Accepted", "NonAuthoritativeInformation", "NoContent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PartialContent", "MultiStatus", "AlreadyReported", "IMUsed", "MultipleChoices", "Ambiguous", "MovedPermanently", "Moved", "Found", "Redirect", "<PERSON><PERSON><PERSON>", "RedirectMethod", "NotModified", "UseProxy", "Unused", "TemporaryRedirect", "RedirectKeepVerb", "PermanentRedirect", "BadRequest", "Unauthorized", "PaymentRequired", "Forbidden", "NotFound", "MethodNotAllowed", "NotAcceptable", "ProxyAuthenticationRequired", "RequestTimeout", "Conflict", "Gone", "LengthRequired", "PreconditionFailed", "RequestEntityTooLarge", "RequestUriTooLong", "UnsupportedMediaType", "RequestedRangeNotSatisfiable", "ExpectationFailed", "MisdirectedRequest", "UnprocessableEntity", "UnprocessableContent", "Locked", "FailedDependency", "UpgradeRequired", "PreconditionRequired", "TooManyRequests", "RequestHeaderFields<PERSON>ooLarge", "UnavailableForLegalReasons", "InternalServerError", "NotImplemented", "BadGateway", "ServiceUnavailable", "GatewayTimeout", "HttpVersionNotSupported", "VariantAlsoNegotiates", "InsufficientStorage", "LoopDetected", "NotExtended", "NetworkAuthenticationRequired"], "type": "integer", "format": "int32"}, "LogProcessamento": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "fileID": {"type": "integer", "format": "int32"}, "mensagem": {"type": "string", "nullable": true}, "tipoLog": {"type": "string", "nullable": true}, "dataRegistro": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "ModelException": {"type": "object", "properties": {"messages": {"type": "array", "items": {"type": "string"}, "description": "Gets or sets the messages.", "nullable": true}, "hasMessages": {"type": "boolean", "description": "Gets a value indicating whether this instance has messages.", "readOnly": true}}, "additionalProperties": false, "description": "Model exception class"}, "ObjectApiOutput": {"type": "object", "properties": {"code": {"$ref": "#/components/schemas/HttpStatusCode"}, "description": {"type": "string", "description": "Gets or sets the description.", "nullable": true}, "value": {"description": "Gets or sets the value.", "nullable": true}, "error": {"type": "boolean", "description": "Gets or sets a value indicating whether this RnD.BackEnd.API.Model.Generic.ApiOutput`1 is error."}, "exceptionMessages": {"$ref": "#/components/schemas/ModelException"}, "properties": {"type": "object", "additionalProperties": {"nullable": true}, "description": "Gets or sets the properties.", "nullable": true}}, "additionalProperties": false, "description": "API output class"}, "Operation": {"type": "object", "properties": {"operationType": {"$ref": "#/components/schemas/OperationType"}, "path": {"type": "string", "nullable": true}, "op": {"type": "string", "nullable": true}, "from": {"type": "string", "nullable": true}, "value": {"nullable": true}}, "additionalProperties": false}, "OperationType": {"enum": ["Add", "Remove", "Replace", "Move", "Copy", "Test", "Invalid"], "type": "integer", "format": "int32"}, "Permission": {"required": ["name"], "type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"maxLength": 126, "minLength": 1, "type": "string"}, "userPermissions": {"type": "array", "items": {"$ref": "#/components/schemas/UserPermission"}, "nullable": true}}, "additionalProperties": false}, "ReassignTaskDto": {"type": "object", "properties": {"id": {"type": "string", "description": "Gets or sets the task identifier.", "format": "uuid"}, "newUserId": {"type": "string", "description": "Gets or sets the new userid for the task.", "nullable": true}, "comments": {"type": "string", "description": "Gets or sets the comments.", "nullable": true}, "emailRequest": {"$ref": "#/components/schemas/WorkflowEmailDto"}}, "additionalProperties": false, "description": "Reassign task DTO class"}, "ServiceOutput": {"type": "object", "properties": {"description": {"type": "string", "nullable": true}, "error": {"type": "boolean"}}, "additionalProperties": false}, "SpecificTemplateDto": {"type": "object", "properties": {"templateData": {"description": "Gets or sets the template data.", "nullable": true}, "recipients": {"type": "array", "items": {"type": "string"}, "description": "Gets or sets the recipients.", "nullable": true}, "recipientsCC": {"type": "array", "items": {"type": "string"}, "description": "Gets or sets the recipients cc.", "nullable": true}, "recipientsBCC": {"type": "array", "items": {"type": "string"}, "description": "Gets or sets the recipients BCC.", "nullable": true}, "templateId": {"type": "string", "description": "Gets or sets the template identifier.", "nullable": true}}, "additionalProperties": false, "description": "Specific template identifier DTO class"}, "StartAutomaticWorkflowDto": {"type": "object", "properties": {"workflowIds": {"type": "array", "items": {"type": "string"}, "description": "Gets or sets the workflow ids.", "nullable": true}, "sourceId": {"type": "string", "description": "Gets or sets the source identifier.", "nullable": true}, "sourceUrl": {"type": "string", "description": "Gets or sets the source URL.", "nullable": true}, "sourceMetadata": {"type": "object", "additionalProperties": {"type": "string", "nullable": true}, "description": "Gets or sets the source metadata.", "nullable": true}, "emailRequest": {"$ref": "#/components/schemas/WorkflowEmailDto"}}, "additionalProperties": false, "description": "Start automatic workflow request DTO class"}, "StartWorkflowDto": {"type": "object", "properties": {"displayName": {"type": "string", "description": "Gets or sets the display name.", "nullable": true}, "sourceId": {"type": "string", "description": "Gets or sets the source identifier.", "nullable": true}, "sourceUrl": {"type": "string", "description": "Gets or sets the source URL.", "nullable": true}, "sourceType": {"type": "string", "description": "Gets or sets the type of the source.", "nullable": true}, "workflowType": {"type": "string", "description": "Gets or sets the type of the workflow.", "nullable": true}, "purpose": {"type": "string", "description": "Gets or sets the purpose.", "nullable": true}, "userId": {"type": "string", "description": "Gets or sets the user identifier.", "nullable": true}, "autoComplete": {"type": "boolean", "description": "Gets or sets a value indicating whether [automatic complete]."}, "workflowStatus": {"type": "string", "description": "Gets or sets the workflow status.", "nullable": true}, "steps": {"type": "array", "items": {"$ref": "#/components/schemas/CustomStepDto"}, "description": "Gets or sets the steps.", "nullable": true}, "emailRequest": {"$ref": "#/components/schemas/WorkflowEmailDto"}}, "additionalProperties": false, "description": "Start workflow request DTO class"}, "StringObjectDictionaryApiOutput": {"type": "object", "properties": {"code": {"$ref": "#/components/schemas/HttpStatusCode"}, "description": {"type": "string", "description": "Gets or sets the description.", "nullable": true}, "value": {"type": "object", "additionalProperties": {"nullable": true}, "description": "Gets or sets the value.", "nullable": true}, "error": {"type": "boolean", "description": "Gets or sets a value indicating whether this RnD.BackEnd.API.Model.Generic.ApiOutput`1 is error."}, "exceptionMessages": {"$ref": "#/components/schemas/ModelException"}, "properties": {"type": "object", "additionalProperties": {"nullable": true}, "description": "Gets or sets the properties.", "nullable": true}}, "additionalProperties": false, "description": "API output class"}, "StringObjectDictionaryListApiOutput": {"type": "object", "properties": {"code": {"$ref": "#/components/schemas/HttpStatusCode"}, "description": {"type": "string", "description": "Gets or sets the description.", "nullable": true}, "value": {"type": "array", "items": {"type": "object", "additionalProperties": {}}, "description": "Gets or sets the value.", "nullable": true}, "error": {"type": "boolean", "description": "Gets or sets a value indicating whether this RnD.BackEnd.API.Model.Generic.ApiOutput`1 is error."}, "exceptionMessages": {"$ref": "#/components/schemas/ModelException"}, "properties": {"type": "object", "additionalProperties": {"nullable": true}, "description": "Gets or sets the properties.", "nullable": true}}, "additionalProperties": false, "description": "API output class"}, "Subscription": {"required": ["subscriptionID"], "type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "subscriptionID": {"maxLength": 255, "minLength": 1, "type": "string"}, "consumos": {"type": "array", "items": {"$ref": "#/components/schemas/ConsumoSubscricao"}, "nullable": true}}, "additionalProperties": false}, "TaskStatus": {"enum": ["TODO", "CANCELLED", "APPROVED", "REJECTED", "FROWARDED"], "type": "integer", "description": "Task status possible for Workflow DTO enum", "format": "int32"}, "UpdateTaskDto": {"type": "object", "properties": {"id": {"type": "string", "description": "Gets or sets the identifier.", "format": "uuid"}, "taskStatus": {"type": "string", "description": "Gets or sets the task status.", "nullable": true}, "taskCompleted": {"type": "boolean", "description": "Represents an event that is raised when a task either successfully or unsuccessfully completes.", "nullable": true}, "comments": {"type": "string", "description": "Gets or sets the comments.", "nullable": true}, "emailRequest": {"$ref": "#/components/schemas/WorkflowEmailDto"}}, "additionalProperties": false, "description": "Update task DTO class"}, "UpdateTaskOutputDto": {"type": "object", "properties": {"workflowStatus": {"type": "string", "description": "Gets or sets the workflow status.", "nullable": true}, "workflowPurpose": {"type": "string", "description": "Gets or sets the workflow purpose.", "nullable": true}, "sourceType": {"type": "string", "description": "Gets or sets the type of the source.", "nullable": true}, "taskId": {"type": "string", "description": "Gets or sets the task identifier.", "format": "uuid", "nullable": true}, "workflowInstanceId": {"type": "string", "description": "Gets or sets the workflow instance identifier.", "format": "uuid"}}, "additionalProperties": false, "description": "Update task output Dto"}, "UpdateTaskOutputDtoListApiOutput": {"type": "object", "properties": {"code": {"$ref": "#/components/schemas/HttpStatusCode"}, "description": {"type": "string", "description": "Gets or sets the description.", "nullable": true}, "value": {"type": "array", "items": {"$ref": "#/components/schemas/UpdateTaskOutputDto"}, "description": "Gets or sets the value.", "nullable": true}, "error": {"type": "boolean", "description": "Gets or sets a value indicating whether this RnD.BackEnd.API.Model.Generic.ApiOutput`1 is error."}, "exceptionMessages": {"$ref": "#/components/schemas/ModelException"}, "properties": {"type": "object", "additionalProperties": {"nullable": true}, "description": "Gets or sets the properties.", "nullable": true}}, "additionalProperties": false, "description": "API output class"}, "User": {"required": ["azureId", "email", "username"], "type": "object", "properties": {"userID": {"type": "integer", "format": "int32"}, "username": {"maxLength": 126, "minLength": 1, "type": "string"}, "email": {"maxLength": 126, "minLength": 1, "type": "string", "format": "email"}, "azureId": {"maxLength": 255, "minLength": 1, "type": "string"}, "userPermissions": {"type": "array", "items": {"$ref": "#/components/schemas/UserPermission"}, "nullable": true}}, "additionalProperties": false}, "UserPermission": {"type": "object", "properties": {"userID": {"type": "integer", "format": "int32"}, "permissionID": {"type": "integer", "format": "int32"}, "user": {"$ref": "#/components/schemas/User"}, "permission": {"$ref": "#/components/schemas/Permission"}}, "additionalProperties": false}, "UserRegisterRequest": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "azureId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "VehicleDto": {"type": "object", "properties": {"id": {"type": "string", "description": "Gets or sets the identifier.", "nullable": true}, "brandId": {"type": "string", "description": "Gets or sets the brand identifier.", "nullable": true}, "brand": {"type": "string", "description": "Gets or sets the brand.", "nullable": true}, "modelName": {"type": "string", "description": "Gets or sets the name of the model.", "nullable": true}, "fuelType": {"type": "string", "description": "Gets or sets the type of the fuel.", "nullable": true}, "version": {"type": "string", "description": "Gets or sets the version.", "nullable": true}, "year": {"type": "integer", "description": "Gets or sets the year.", "format": "int32"}}, "additionalProperties": false, "description": "Vehicle DTO class"}, "VehicleDtoApiOutput": {"type": "object", "properties": {"code": {"$ref": "#/components/schemas/HttpStatusCode"}, "description": {"type": "string", "description": "Gets or sets the description.", "nullable": true}, "value": {"$ref": "#/components/schemas/VehicleDto"}, "error": {"type": "boolean", "description": "Gets or sets a value indicating whether this RnD.BackEnd.API.Model.Generic.ApiOutput`1 is error."}, "exceptionMessages": {"$ref": "#/components/schemas/ModelException"}, "properties": {"type": "object", "additionalProperties": {"nullable": true}, "description": "Gets or sets the properties.", "nullable": true}}, "additionalProperties": false, "description": "API output class"}, "VehicleDtoListApiOutput": {"type": "object", "properties": {"code": {"$ref": "#/components/schemas/HttpStatusCode"}, "description": {"type": "string", "description": "Gets or sets the description.", "nullable": true}, "value": {"type": "array", "items": {"$ref": "#/components/schemas/VehicleDto"}, "description": "Gets or sets the value.", "nullable": true}, "error": {"type": "boolean", "description": "Gets or sets a value indicating whether this RnD.BackEnd.API.Model.Generic.ApiOutput`1 is error."}, "exceptionMessages": {"$ref": "#/components/schemas/ModelException"}, "properties": {"type": "object", "additionalProperties": {"nullable": true}, "description": "Gets or sets the properties.", "nullable": true}}, "additionalProperties": false, "description": "API output class"}, "WeatherDto": {"type": "object", "properties": {"id": {"type": "string", "description": "Gets or sets the identifier.", "format": "uuid"}, "temperature": {"type": "integer", "description": "Gets or sets the temperature.", "format": "int32"}, "summary": {"type": "string", "description": "Gets or sets the summary.", "nullable": true}, "location": {"type": "string", "description": "Gets or sets the location.", "nullable": true}}, "additionalProperties": false, "description": "Weather DTO class"}, "WeatherDtoApiOutput": {"type": "object", "properties": {"code": {"$ref": "#/components/schemas/HttpStatusCode"}, "description": {"type": "string", "description": "Gets or sets the description.", "nullable": true}, "value": {"$ref": "#/components/schemas/WeatherDto"}, "error": {"type": "boolean", "description": "Gets or sets a value indicating whether this RnD.BackEnd.API.Model.Generic.ApiOutput`1 is error."}, "exceptionMessages": {"$ref": "#/components/schemas/ModelException"}, "properties": {"type": "object", "additionalProperties": {"nullable": true}, "description": "Gets or sets the properties.", "nullable": true}}, "additionalProperties": false, "description": "API output class"}, "WeatherDtoListApiOutput": {"type": "object", "properties": {"code": {"$ref": "#/components/schemas/HttpStatusCode"}, "description": {"type": "string", "description": "Gets or sets the description.", "nullable": true}, "value": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherDto"}, "description": "Gets or sets the value.", "nullable": true}, "error": {"type": "boolean", "description": "Gets or sets a value indicating whether this RnD.BackEnd.API.Model.Generic.ApiOutput`1 is error."}, "exceptionMessages": {"$ref": "#/components/schemas/ModelException"}, "properties": {"type": "object", "additionalProperties": {"nullable": true}, "description": "Gets or sets the properties.", "nullable": true}}, "additionalProperties": false, "description": "API output class"}, "WorkflowDefinitionDto": {"type": "object", "properties": {"id": {"type": "string", "description": "Gets or sets the identifier.", "format": "uuid", "nullable": true}, "displayName": {"type": "string", "description": "Gets or sets the display name.", "nullable": true}, "rulesConditionType": {"type": "string", "description": "Gets or sets the type of the rules condition.", "nullable": true}, "sourceType": {"type": "string", "description": "Gets or sets the type of the source.", "nullable": true}, "workflowType": {"type": "string", "description": "Gets or sets the type of the workflow.", "nullable": true}, "purpose": {"type": "string", "description": "Gets or sets the purpose.", "nullable": true}, "numberOfSteps": {"type": "integer", "description": "Gets or sets the number of steps.", "format": "int32"}, "numberOfRules": {"type": "integer", "description": "Gets or sets the number of rules.", "format": "int32"}, "isActive": {"type": "boolean", "description": "Gets or sets a value indicating whether this instance is active."}}, "additionalProperties": false, "description": "Workflow definition DTO class"}, "WorkflowDefinitionDtoIListApiOutput": {"type": "object", "properties": {"code": {"$ref": "#/components/schemas/HttpStatusCode"}, "description": {"type": "string", "description": "Gets or sets the description.", "nullable": true}, "value": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowDefinitionDto"}, "description": "Gets or sets the value.", "nullable": true}, "error": {"type": "boolean", "description": "Gets or sets a value indicating whether this RnD.BackEnd.API.Model.Generic.ApiOutput`1 is error."}, "exceptionMessages": {"$ref": "#/components/schemas/ModelException"}, "properties": {"type": "object", "additionalProperties": {"nullable": true}, "description": "Gets or sets the properties.", "nullable": true}}, "additionalProperties": false, "description": "API output class"}, "WorkflowEmailDto": {"type": "object", "properties": {"templateId": {"type": "string", "description": "Gets or sets the template identifier.", "nullable": true}, "recipients": {"type": "array", "items": {"type": "string"}, "description": "Gets or sets the recipients.", "nullable": true}, "templateData": {"description": "Gets or sets the template data.", "nullable": true}, "subject": {"type": "string", "description": "Gets or sets the subject.", "nullable": true}, "sendEmail": {"type": "boolean", "description": "Gets or sets a value indicating whether [send email]."}}, "additionalProperties": false, "description": "Workflow Email DTO class"}, "WorkflowInstanceBySourceIdsDto": {"type": "object", "properties": {"sourceIds": {"type": "array", "items": {"type": "string"}, "description": "Gets or sets the source ids.", "nullable": true}, "status": {"type": "array", "items": {"type": "string"}, "description": "Gets or sets the status.", "nullable": true}}, "additionalProperties": false, "description": "Workflow Instances by SourceId DTO class"}, "WorkflowInstanceDto": {"type": "object", "properties": {"id": {"type": "string", "description": "Gets or sets the identifier.", "format": "uuid", "nullable": true}, "workflowId": {"type": "string", "description": "Gets or sets the workflow identifier.", "format": "uuid"}, "displayName": {"type": "string", "description": "Gets or sets the display name.", "nullable": true}, "sourceId": {"type": "string", "description": "Gets or sets the source identifier.", "nullable": true}, "sourceUrl": {"type": "string", "description": "Gets or sets the source URL.", "nullable": true}, "workflowStatus": {"type": "string", "description": "Gets or sets the workflow status.", "nullable": true}, "sourceType": {"type": "string", "description": "Gets or sets the type of the source.", "nullable": true}, "workflowType": {"type": "string", "description": "Gets or sets the type of the workflow.", "nullable": true}, "purpose": {"type": "string", "description": "Gets or sets the purpose.", "nullable": true}, "stateProcessingRequired": {"type": "boolean", "description": "Gets or sets the state processing required.", "nullable": true}, "activeStepName": {"type": "string", "description": "Gets or sets the name of the active step.", "nullable": true}, "steps": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowInstanceStepDto"}, "description": "Gets or sets the steps.", "nullable": true}, "sysCreateUserId": {"type": "string", "description": "Gets or sets the system create user identifier.", "nullable": true}, "sysModifyDate": {"type": "string", "description": "Gets or sets the system modify date.", "format": "date-time"}}, "additionalProperties": false, "description": "Workflow instance DTO class"}, "WorkflowInstanceDtoApiOutput": {"type": "object", "properties": {"code": {"$ref": "#/components/schemas/HttpStatusCode"}, "description": {"type": "string", "description": "Gets or sets the description.", "nullable": true}, "value": {"$ref": "#/components/schemas/WorkflowInstanceDto"}, "error": {"type": "boolean", "description": "Gets or sets a value indicating whether this RnD.BackEnd.API.Model.Generic.ApiOutput`1 is error."}, "exceptionMessages": {"$ref": "#/components/schemas/ModelException"}, "properties": {"type": "object", "additionalProperties": {"nullable": true}, "description": "Gets or sets the properties.", "nullable": true}}, "additionalProperties": false, "description": "API output class"}, "WorkflowInstanceDtoIListApiOutput": {"type": "object", "properties": {"code": {"$ref": "#/components/schemas/HttpStatusCode"}, "description": {"type": "string", "description": "Gets or sets the description.", "nullable": true}, "value": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowInstanceDto"}, "description": "Gets or sets the value.", "nullable": true}, "error": {"type": "boolean", "description": "Gets or sets a value indicating whether this RnD.BackEnd.API.Model.Generic.ApiOutput`1 is error."}, "exceptionMessages": {"$ref": "#/components/schemas/ModelException"}, "properties": {"type": "object", "additionalProperties": {"nullable": true}, "description": "Gets or sets the properties.", "nullable": true}}, "additionalProperties": false, "description": "API output class"}, "WorkflowInstanceStepDto": {"type": "object", "properties": {"id": {"type": "string", "description": "Gets or sets the identifier.", "format": "uuid"}, "stepId": {"type": "string", "description": "Gets or sets the step identifier.", "format": "uuid", "nullable": true}, "instanceId": {"type": "string", "description": "Gets or sets the instance identifier.", "format": "uuid"}, "displayName": {"type": "string", "description": "Gets or sets the display name.", "nullable": true}, "allUsersMustApprove": {"type": "boolean", "description": "Gets or sets a value indicating whether [all users must approve]."}, "skipOverOtherTasks": {"type": "boolean", "description": "Gets or sets a value indicating whether [skip over other tasks]."}, "order": {"type": "integer", "description": "Gets or sets the order.", "format": "int32"}, "isActive": {"type": "boolean", "description": "Gets or sets a value indicating whether this instance is active."}, "stepStatus": {"type": "string", "description": "Gets or sets the step status.", "nullable": true}, "numberOfActionsRequired": {"type": "integer", "description": "Gets or sets the number of actions required.", "format": "int32"}, "numberOfActionsCompleted": {"type": "integer", "description": "Gets or sets the number of actions completed.", "format": "int32"}, "tasks": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowInstanceTaskDto"}, "description": "Gets or sets the tasks.", "nullable": true}}, "additionalProperties": false, "description": "Workflow instance step DTO class"}, "WorkflowInstanceTaskDto": {"type": "object", "properties": {"id": {"type": "string", "description": "Gets or sets the identifier.", "format": "uuid", "nullable": true}, "userId": {"type": "string", "description": "Gets or sets the user identifier.", "nullable": true}, "taskStatus": {"type": "string", "description": "Gets or sets the task status.", "nullable": true}, "taskCompleted": {"type": "boolean", "description": "Represents an event that is raised when a task either successfully or unsuccessfully completes."}, "taskActivated": {"type": "boolean", "description": "Gets or sets a value indicating whether [task activated]."}, "comments": {"type": "string", "description": "Gets or sets the comments.", "nullable": true}, "sysModifyDate": {"type": "string", "description": "Gets or sets the system modify date.", "format": "date-time"}}, "additionalProperties": false, "description": "Workflow instance task DTO class"}, "WorkflowInstancesListDto": {"type": "object", "properties": {"workflowInstances": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowInstanceDto"}, "description": "Gets or sets the workflow instances.", "nullable": true}, "totalRows": {"type": "integer", "description": "Gets or sets the total rows.", "format": "int32"}}, "additionalProperties": false, "description": "Workflow instances list DTO class"}, "WorkflowInstancesListDtoIListApiOutput": {"type": "object", "properties": {"code": {"$ref": "#/components/schemas/HttpStatusCode"}, "description": {"type": "string", "description": "Gets or sets the description.", "nullable": true}, "value": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowInstancesListDto"}, "description": "Gets or sets the value.", "nullable": true}, "error": {"type": "boolean", "description": "Gets or sets a value indicating whether this RnD.BackEnd.API.Model.Generic.ApiOutput`1 is error."}, "exceptionMessages": {"$ref": "#/components/schemas/ModelException"}, "properties": {"type": "object", "additionalProperties": {"nullable": true}, "description": "Gets or sets the properties.", "nullable": true}}, "additionalProperties": false, "description": "API output class"}, "WorkflowPurpose": {"enum": ["APPROVAL", "COLLECT_FEEDBACK", "INTEGRATION"], "type": "integer", "description": "Purpose possible for Workflow DTO enum", "format": "int32"}, "WorkflowStatus": {"enum": ["CANCELLED", "IN_PROGRESS", "APPROVED", "REJECTED"], "type": "integer", "description": "Status possible for Workflow DTO enum", "format": "int32"}, "WorkflowTaskDto": {"type": "object", "properties": {"id": {"type": "string", "description": "Gets or sets the identifier.", "format": "uuid", "nullable": true}, "workflowInstanceId": {"type": "string", "description": "Gets or sets the workflow instance identifier.", "format": "uuid"}, "stepId": {"type": "string", "description": "Gets or sets the step identifier.", "format": "uuid"}, "userId": {"type": "string", "description": "Gets or sets the user identifier.", "nullable": true}, "taskStatus": {"type": "string", "description": "Gets or sets the task status.", "nullable": true}, "taskCompleted": {"type": "boolean", "description": "Represents an event that is raised when a task either successfully or unsuccessfully completes."}, "taskActivated": {"type": "boolean", "description": "Gets or sets a value indicating whether [task activated]."}, "comments": {"type": "string", "description": "Gets or sets the comments.", "nullable": true}, "sourceId": {"type": "string", "description": "Gets or sets the source identifier.", "nullable": true}, "sourceUrl": {"type": "string", "description": "Gets or sets the source URL.", "nullable": true}, "workflowStatus": {"type": "string", "description": "Gets or sets the workflow status.", "nullable": true}, "workflowName": {"type": "string", "description": "Gets or sets the name of the workflow.", "nullable": true}, "stepName": {"type": "string", "description": "Gets or sets the name of the step.", "nullable": true}, "stepOrder": {"type": "integer", "description": "Gets or sets the step order.", "format": "int32"}, "sysModifyDate": {"type": "string", "description": "Gets or sets the system modify date.", "format": "date-time"}, "totalRows": {"type": "integer", "description": "Gets or sets the total rows.", "format": "int32"}, "userAllowedToEdit": {"type": "boolean", "description": "Gets or sets a value indicating whether [user allowed to edit]."}}, "additionalProperties": false, "description": "Workflow task DTO class"}, "WorkflowTaskDtoApiOutput": {"type": "object", "properties": {"code": {"$ref": "#/components/schemas/HttpStatusCode"}, "description": {"type": "string", "description": "Gets or sets the description.", "nullable": true}, "value": {"$ref": "#/components/schemas/WorkflowTaskDto"}, "error": {"type": "boolean", "description": "Gets or sets a value indicating whether this RnD.BackEnd.API.Model.Generic.ApiOutput`1 is error."}, "exceptionMessages": {"$ref": "#/components/schemas/ModelException"}, "properties": {"type": "object", "additionalProperties": {"nullable": true}, "description": "Gets or sets the properties.", "nullable": true}}, "additionalProperties": false, "description": "API output class"}, "WorkflowTasksListDto": {"type": "object", "properties": {"tasks": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowTaskDto"}, "description": "Gets or sets the tasks.", "nullable": true}, "totalRows": {"type": "integer", "description": "Gets or sets the total rows.", "format": "int32"}}, "additionalProperties": false, "description": "Workflow tasks list DTO class"}, "WorkflowTasksListDtoApiOutput": {"type": "object", "properties": {"code": {"$ref": "#/components/schemas/HttpStatusCode"}, "description": {"type": "string", "description": "Gets or sets the description.", "nullable": true}, "value": {"$ref": "#/components/schemas/WorkflowTasksListDto"}, "error": {"type": "boolean", "description": "Gets or sets a value indicating whether this RnD.BackEnd.API.Model.Generic.ApiOutput`1 is error."}, "exceptionMessages": {"$ref": "#/components/schemas/ModelException"}, "properties": {"type": "object", "additionalProperties": {"nullable": true}, "description": "Gets or sets the properties.", "nullable": true}}, "additionalProperties": false, "description": "API output class"}, "WorkflowType": {"enum": ["AUTOMATIC", "MANUAL"], "type": "integer", "description": "Types possible for Workflow DTO enum", "format": "int32"}}, "securitySchemes": {"Bearer": {"type": "http", "description": "JWT Authorization header using the Bear<PERSON> scheme.", "scheme": "bearer"}}}, "security": [{"Bearer": []}]}